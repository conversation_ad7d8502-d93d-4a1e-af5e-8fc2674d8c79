# MPhotos 代码规范

## 🎯 总体原则
- **可读性优先**: 代码应自解释，减少注释依赖
- **性能导向**: 优化内存使用和 UI 响应
- **架构一致**: 严格遵循 MVVM-C 模式
- **简洁高效**: 减少 token 使用，提高 AI 开发效率

## 📁 项目结构规范

### 目录组织
```
MPhotos/
├── App/                    # 应用配置
├── Core/                   # 核心工具、扩展
├── Data/                   # 数据层
│   ├── Models/            # 数据模型
│   ├── Cache/             # 缓存系统
│   └── Repositories/      # 数据仓库
├── Domain/                # 业务逻辑层
│   ├── UseCases/          # 用例
│   ├── Services/          # 服务
│   └── Managers/          # 管理器
└── Presentation/          # 展示层
    ├── Coordinators/      # 导航协调器
    ├── Scenes/            # 场景模块
    └── Common/            # 公共UI组件
```

### 文件命名
- **类文件**: `PascalCase.swift` (e.g., `MediaGridViewController.swift`)
- **协议**: `PascalCase + Protocol/ing` (e.g., `MediaManaging.swift`)
- **扩展**: `Type+Extension.swift` (e.g., `UIView+Animation.swift`)
- **枚举**: `PascalCase.swift` (e.g., `AssetType.swift`)

## 💾 Swift 代码规范

### 命名约定
```swift
// ✅ 推荐
class MediaGridViewModel: BaseViewModel {
    @Published var assets: [PHAsset] = []
    private let mediaManager: MediaManaging
    
    func fetchAssets() async { }
    private func handleError(_ error: Error) { }
}

// ❌ 避免
class mediaGridVM: NSObject {
    var data: [Any] = []
    var mgr: AnyObject?
    
    func getData() { }
    func err_handle(_ e: Any) { }
}
```

### 类型使用规范
```swift
// ✅ 优先使用 Swift 原生类型
var assets: [PHAsset] = []
var title: String = ""
var isLoading: Bool = false

// ❌ 避免 NS 类型
var assets: NSArray = []
var title: NSString = ""
var isLoading: NSNumber = false
```

### 访问控制
```swift
// ✅ 明确访问级别
public protocol MediaManaging { }
internal class MediaManager: MediaManaging { }
private func validateAsset(_ asset: PHAsset) -> Bool { }
fileprivate extension UIView { }

// 默认规则：
// - 协议: public
// - 类/结构体: internal  
// - 属性/方法: internal（对外暴露）或 private（内部使用）
```

## 🏗️ 架构模式规范

### MVVM-C 实现
```swift
// ViewModel 标准结构
class FeatureViewModel: BaseViewModel {
    // MARK: - Published Properties
    @Published var items: [DataType] = []
    
    // MARK: - Dependencies
    private let service: ServiceType
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(service: ServiceType) {
        self.service = service
        super.init()
    }
    
    // MARK: - Public Methods
    func loadData() { }
    
    // MARK: - Private Methods
    private func handleResponse(_ response: DataType) { }
}
```

### Coordinator 模式
```swift
protocol Coordinating: AnyObject {
    var navigationController: UINavigationController { get }
    var childCoordinators: [Coordinating] { get set }
    
    func start()
    func coordinate(to coordinator: Coordinating)
}

class FeatureCoordinator: Coordinating {
    let navigationController: UINavigationController
    var childCoordinators: [Coordinating] = []
    
    func start() {
        let viewModel = FeatureViewModel(service: serviceContainer.resolve())
        let viewController = FeatureViewController(viewModel: viewModel)
        navigationController.pushViewController(viewController, animated: true)
    }
}
```

### 依赖注入
```swift
// 协议定义
protocol ServiceContainer {
    func resolve<T>() -> T
    func register<T>(_ service: T, for type: T.Type)
}

// 使用示例
class FeatureViewController: UIViewController {
    private let viewModel: FeatureViewModel
    
    init(viewModel: FeatureViewModel) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
}
```

## 🎨 UI 开发规范

### UIKit 组件
```swift
// ✅ 推荐：编程式布局
class MediaCell: UICollectionViewCell {
    private lazy var imageView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        view.clipsToBounds = true
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    private func setupViews() {
        addSubview(imageView)
        imageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: topAnchor),
            imageView.leadingAnchor.constraint(equalTo: leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
}
```

### 性能优化规范
```swift
// Cell 复用优化
override func prepareForReuse() {
    super.prepareForReuse()
    
    // 取消图片请求
    if let requestID = currentRequestID {
        PHImageManager.default().cancelImageRequest(requestID)
        currentRequestID = nil
    }
    
    // 清理 UI
    imageView.image = nil
    titleLabel.text = nil
}

// 内存优化
deinit {
    cancellables.removeAll()
    NotificationCenter.default.removeObserver(self)
}
```

## ⚡ 性能编码规范

### 异步编程
```swift
// ✅ 推荐：Swift Concurrency
func loadAssets() async throws -> [PHAsset] {
    return try await withCheckedThrowingContinuation { continuation in
        mediaManager.fetchAssets { result in
            continuation.resume(with: result)
        }
    }
}

// ✅ 推荐：Combine
func loadData() -> AnyPublisher<[DataType], Error> {
    service.fetchData()
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
}
```

### 内存管理
```swift
// ✅ 使用 weak self
service.fetchData { [weak self] result in
    guard let self = self else { return }
    self.handleResult(result)
}

// ✅ 合理使用 lazy
private lazy var expensiveCalculation: ExpensiveType = {
    return ExpensiveType.calculateValue()
}()
```

## 🧪 测试代码规范

### 命名约定
```swift
// 测试类命名：FeatureNameTests
class MediaManagerTests: XCTestCase { }

// 测试方法命名：test + 场景 + 预期结果
func testFetchAssets_WithValidAlbum_ReturnsAssets() { }
func testFetchAssets_WithInvalidAlbum_ThrowsError() { }
```

### 测试结构
```swift
func testFeature_Scenario_ExpectedResult() {
    // Given - 准备测试数据
    let expectedAssets = createMockAssets()
    mockService.assetsToReturn = expectedAssets
    
    // When - 执行被测试方法
    viewModel.fetchAssets()
    
    // Then - 验证结果
    XCTAssertEqual(viewModel.assets.count, expectedAssets.count)
    XCTAssertFalse(viewModel.isLoading)
    XCTAssertNil(viewModel.error)
}
```

## 📝 代码注释规范

### 最小化注释原则
```swift
// ✅ 代码自解释，无需注释
func calculateOptimalImageSize(for cellSize: CGSize) -> CGSize {
    let scale = UIScreen.main.scale
    return CGSize(
        width: cellSize.width * scale * 1.5,
        height: cellSize.height * scale * 1.5
    )
}

// ✅ 必要时添加简洁注释
// GPU 友好的尺寸对齐
let alignedWidth = ceil(targetSize.width / 16) * 16
```

### 文档注释
```swift
/// 请求指定资源的缩略图
/// - Parameters:
///   - asset: 目标资源
///   - size: 缩略图尺寸
/// - Returns: 图片请求 ID，用于取消请求
func requestThumbnail(for asset: PHAsset, size: CGSize) -> PHImageRequestID
```

## 🔧 工具和配置

### SwiftLint 配置 (`.swiftlint.yml`)
```yaml
disabled_rules:
  - trailing_whitespace
  - line_length

opt_in_rules:
  - empty_count
  - force_unwrapping
  - implicit_return

line_length: 120
file_length: 500
```

### Xcode 格式化
- **缩进**: 4 空格
- **行长度**: 120 字符
- **空行**: 类之间 2 行，方法之间 1 行

## ✅ 代码审查清单

### 基础检查
- [ ] 命名符合规范
- [ ] 访问控制正确
- [ ] 无强制解包
- [ ] 无循环引用
- [ ] 错误处理完整

### 性能检查  
- [ ] 合理使用缓存
- [ ] 主线程更新 UI
- [ ] 及时释放资源
- [ ] 避免重复计算

### 架构检查
- [ ] 职责单一
- [ ] 依赖注入正确
- [ ] 协议使用合理
- [ ] 测试覆盖充分

---

**记住**: 好的代码应该让 AI 工具能够轻松理解和扩展，同时确保人类开发者的可维护性。