import UIKit

class TopToolbar: UIView {
    
    // MARK: - UI Components
    private let blurEffect = UIBlurEffect(style: .systemUltraThinMaterial)
    private lazy var blurEffectView = UIVisualEffectView(effect: blurEffect)
    private lazy var vibrancyEffectView = UIVisualEffectView(effect: UIVibrancyEffect(blurEffect: blurEffect))
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 34, weight: .bold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private lazy var selectButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("选择", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 17, weight: .regular)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private lazy var moreButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "ellipsis.circle"), for: .normal)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    // MARK: - Properties
    var title: String = "图库" {
        didSet {
            titleLabel.text = title
        }
    }
    
    var onSelectTapped: (() -> Void)?
    var onMoreTapped: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    // MARK: - Setup
    private func setupView() {
        backgroundColor = UIColor.clear
        
        // 添加毛玻璃背景
        addSubview(blurEffectView)
        blurEffectView.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加活力效果视图
        blurEffectView.contentView.addSubview(vibrancyEffectView)
        vibrancyEffectView.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加控件到活力效果视图
        vibrancyEffectView.contentView.addSubview(titleLabel)
        vibrancyEffectView.contentView.addSubview(selectButton)
        vibrancyEffectView.contentView.addSubview(moreButton)
        
        setupConstraints()
        setupActions()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 毛玻璃背景约束
            blurEffectView.topAnchor.constraint(equalTo: topAnchor),
            blurEffectView.leadingAnchor.constraint(equalTo: leadingAnchor),
            blurEffectView.trailingAnchor.constraint(equalTo: trailingAnchor),
            blurEffectView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // 活力效果视图约束
            vibrancyEffectView.topAnchor.constraint(equalTo: blurEffectView.contentView.topAnchor),
            vibrancyEffectView.leadingAnchor.constraint(equalTo: blurEffectView.contentView.leadingAnchor),
            vibrancyEffectView.trailingAnchor.constraint(equalTo: blurEffectView.contentView.trailingAnchor),
            vibrancyEffectView.bottomAnchor.constraint(equalTo: blurEffectView.contentView.bottomAnchor),
            
            // 标题标签约束
            titleLabel.leadingAnchor.constraint(equalTo: vibrancyEffectView.contentView.leadingAnchor, constant: 16),
            titleLabel.centerYAnchor.constraint(equalTo: vibrancyEffectView.contentView.centerYAnchor),
            
            // 更多按钮约束
            moreButton.trailingAnchor.constraint(equalTo: vibrancyEffectView.contentView.trailingAnchor, constant: -16),
            moreButton.centerYAnchor.constraint(equalTo: vibrancyEffectView.contentView.centerYAnchor),
            moreButton.widthAnchor.constraint(equalToConstant: 44),
            moreButton.heightAnchor.constraint(equalToConstant: 44),
            
            // 选择按钮约束
            selectButton.trailingAnchor.constraint(equalTo: moreButton.leadingAnchor, constant: -8),
            selectButton.centerYAnchor.constraint(equalTo: vibrancyEffectView.contentView.centerYAnchor),
            selectButton.widthAnchor.constraint(greaterThanOrEqualToConstant: 44),
            selectButton.heightAnchor.constraint(equalToConstant: 44),
            
            // 标题与选择按钮之间的约束
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: selectButton.leadingAnchor, constant: -16)
        ])
    }
    
    private func setupActions() {
        selectButton.addTarget(self, action: #selector(selectButtonTapped), for: .touchUpInside)
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - Actions
    @objc private func selectButtonTapped() {
        onSelectTapped?()
    }
    
    @objc private func moreButtonTapped() {
        onMoreTapped?()
    }
    
    // MARK: - Public Methods
    func setSelectMode(_ isSelecting: Bool) {
        selectButton.setTitle(isSelecting ? "取消" : "选择", for: .normal)
    }
    
    func updateBlurEffect(scrollOffset: CGFloat) {
        // 根据滚动偏移量调整毛玻璃透明度
        let maxOffset: CGFloat = 100
        let alpha = min(1.0, scrollOffset / maxOffset)
        blurEffectView.alpha = 0.7 + (0.3 * alpha) // 最小透明度0.7，最大1.0
    }
}