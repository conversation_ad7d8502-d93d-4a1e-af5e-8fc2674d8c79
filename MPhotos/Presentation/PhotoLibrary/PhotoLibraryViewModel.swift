import Foundation
import PhotosUI
import Combine

@MainActor
class PhotoLibraryViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    @Published var assets: [PHAsset] = []
    @Published var selectedAssets: Set<PHAsset> = []
    
    // MARK: - Services
    let mediaManager: MediaManaging
    let cacheManager: CacheManaging
    
    // MARK: - Private Properties
    private var fetchResult: PHFetchResult<PHAsset>?
    private var photoLibraryObserver: PhotoLibraryObserver?
    
    // MARK: - Initialization
    init(mediaManager: MediaManaging, cacheManager: CacheManaging) {
        self.mediaManager = mediaManager
        self.cacheManager = cacheManager
        super.init()
        
        setupPhotoLibraryChangeObserver()
    }
    
    deinit {
        if let observer = photoLibraryObserver {
            PHPhotoLibrary.shared().unregisterChangeObserver(observer)
        }
    }
    
    // MARK: - Public Methods
    func requestPhotosPermission() {
        Task {
            do {
                let status = try await mediaManager.requestAuthorization()
                
                await MainActor.run {
                    switch status {
                    case .authorized, .limited:
                        self.loadAssets()
                    case .denied, .restricted:
                        self.error = MPhotosError.unauthorized
                    case .notDetermined:
                        break
                    @unknown default:
                        break
                    }
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
    }
    
    func refreshAssets() {
        loadAssets()
    }
    
    func toggleAssetSelection(_ asset: PHAsset) {
        if selectedAssets.contains(asset) {
            selectedAssets.remove(asset)
        } else {
            selectedAssets.insert(asset)
        }
    }
    
    func clearSelection() {
        selectedAssets.removeAll()
    }
    
    // MARK: - Private Methods
    private func loadAssets() {
        isLoading = true
        error = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // 获取所有照片，按时间倒序排列
            let fetchOptions = PHFetchOptions()
            fetchOptions.sortDescriptors = [
                NSSortDescriptor(key: "creationDate", ascending: false)
            ]
            fetchOptions.includeHiddenAssets = false
            
            let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
            self.fetchResult = fetchResult
            
            var assets: [PHAsset] = []
            fetchResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
            DispatchQueue.main.async {
                self.assets = assets
                self.isLoading = false
                
                // 开始预加载缩略图
                self.preloadThumbnails()
            }
        }
    }
    
    private func preloadThumbnails() {
        guard !assets.isEmpty else { return }
        
        // 预加载前20张图片的缩略图
        let preloadCount = min(20, assets.count)
        let assetsToPreload = Array(assets.prefix(preloadCount))
        let cacheManager = self.cacheManager
        
        Task {
            let targetSize = CGSize(width: 200, height: 200)
            
            for asset in assetsToPreload {
                _ = cacheManager.requestImage(for: asset, targetSize: targetSize) { _ in
                    // 图片已缓存，无需处理
                }
            }
        }
    }
    
    private func setupPhotoLibraryChangeObserver() {
        let observer = PhotoLibraryObserver { [weak self] changeInstance in
            self?.handlePhotoLibraryChange(changeInstance)
        }
        self.photoLibraryObserver = observer
        PHPhotoLibrary.shared().register(observer)
    }
    
    private func handlePhotoLibraryChange(_ changeInstance: PHChange) {
        guard let fetchResult = self.fetchResult,
              let changes = changeInstance.changeDetails(for: fetchResult) else {
            return
        }
        
        Task { @MainActor in
            // 更新fetchResult
            self.fetchResult = changes.fetchResultAfterChanges
            
            if changes.hasIncrementalChanges {
                // 增量更新
                self.handleIncrementalChanges(changes)
            } else {
                // 完全重新加载
                self.loadAssets()
            }
        }
    }
    
    private func handleIncrementalChanges(_ changes: PHFetchResultChangeDetails<PHAsset>) {
        var updatedAssets = assets
        
        // 处理删除
        if let removedIndexes = changes.removedIndexes {
            removedIndexes.reversed().forEach { index in
                if index < updatedAssets.count {
                    updatedAssets.remove(at: index)
                }
            }
        }
        
        // 处理插入
        if let insertedIndexes = changes.insertedIndexes {
            insertedIndexes.forEach { index in
                if index <= updatedAssets.count {
                    let asset = changes.fetchResultAfterChanges.object(at: index)
                    updatedAssets.insert(asset, at: index)
                }
            }
        }
        
        // 处理更改
        if let changedIndexes = changes.changedIndexes {
            changedIndexes.forEach { index in
                if index < updatedAssets.count {
                    let asset = changes.fetchResultAfterChanges.object(at: index)
                    updatedAssets[index] = asset
                }
            }
        }
        
        // 处理移动
        changes.enumerateMoves { fromIndex, toIndex in
            if fromIndex < updatedAssets.count && toIndex <= updatedAssets.count {
                let asset = updatedAssets.remove(at: fromIndex)
                updatedAssets.insert(asset, at: toIndex)
            }
        }
        
        assets = updatedAssets
    }
}

// MARK: - Photo Library Observer Helper
private class PhotoLibraryObserver: NSObject, PHPhotoLibraryChangeObserver {
    private let changeHandler: (PHChange) -> Void
    
    init(changeHandler: @escaping (PHChange) -> Void) {
        self.changeHandler = changeHandler
        super.init()
    }
    
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        changeHandler(changeInstance)
    }
}