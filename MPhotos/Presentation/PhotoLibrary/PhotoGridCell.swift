import UIKit
import PhotosUI

class PhotoGridCell: UICollectionViewCell {
    
    static let identifier = "PhotoGridCell"
    
    // MARK: - UI Components
    private lazy var imageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.backgroundColor = UIColor.systemGray6
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()
    
    private lazy var videoIndicator: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(systemName: "play.circle.fill")
        iv.tintColor = UIColor.white
        iv.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        iv.layer.cornerRadius = 12
        iv.isHidden = true
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()
    
    private lazy var durationLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor.white
        label.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        label.textAlignment = .center
        label.isHidden = true
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private lazy var selectionOverlay: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.3)
        view.layer.borderWidth = 3
        view.layer.borderColor = UIColor.systemBlue.cgColor
        view.isHidden = true
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var checkmarkView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(systemName: "checkmark.circle.fill")
        iv.tintColor = UIColor.systemBlue
        iv.backgroundColor = UIColor.white
        iv.layer.cornerRadius = 12
        iv.isHidden = true
        iv.translatesAutoresizingMaskIntoConstraints = false
        return iv
    }()
    
    // MARK: - Properties
    private var asset: PHAsset?
    private var imageRequestID: PHImageRequestID?
    private var cacheManager: CacheManaging?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    // MARK: - Setup
    private func setupView() {
        contentView.addSubview(imageView)
        contentView.addSubview(videoIndicator)
        contentView.addSubview(durationLabel)
        contentView.addSubview(selectionOverlay)
        contentView.addSubview(checkmarkView)
        
        setupConstraints()
        
        // 设置圆角
        layer.cornerRadius = 4
        clipsToBounds = true
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // ImageView约束
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // 视频指示器约束
            videoIndicator.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            videoIndicator.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            videoIndicator.widthAnchor.constraint(equalToConstant: 24),
            videoIndicator.heightAnchor.constraint(equalToConstant: 24),
            
            // 时长标签约束
            durationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            durationLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            durationLabel.heightAnchor.constraint(equalToConstant: 20),
            durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 35),
            
            // 选择覆盖层约束
            selectionOverlay.topAnchor.constraint(equalTo: contentView.topAnchor),
            selectionOverlay.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            selectionOverlay.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            selectionOverlay.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // 选中标记约束
            checkmarkView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            checkmarkView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            checkmarkView.widthAnchor.constraint(equalToConstant: 24),
            checkmarkView.heightAnchor.constraint(equalToConstant: 24)
        ])
    }
    
    // MARK: - Configuration
    func configure(with asset: PHAsset, cacheManager: CacheManaging) {
        self.asset = asset
        self.cacheManager = cacheManager

        // 取消之前的图片请求
        if let requestID = imageRequestID, requestID != PHInvalidImageRequestID {
            PHImageManager.default().cancelImageRequest(requestID)
        }

        // 重置UI状态
        imageView.image = nil
        videoIndicator.isHidden = asset.mediaType != .video
        durationLabel.isHidden = asset.mediaType != .video

        // 设置视频时长
        if asset.mediaType == .video {
            durationLabel.text = formatDuration(asset.duration)
        }

        // 请求缩略图
        requestThumbnail(for: asset, cacheManager: cacheManager)
    }
    
    private func requestThumbnail(for asset: PHAsset, cacheManager: CacheManaging) {
        let targetSize = CGSize(
            width: bounds.width * UIScreen.main.scale,
            height: bounds.height * UIScreen.main.scale
        )
        
        imageRequestID = cacheManager.requestImage(for: asset, targetSize: targetSize) { [weak self] image in
            DispatchQueue.main.async {
                guard self?.asset == asset else { return } // 确保cell没有被重用
                self?.imageView.image = image
            }
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    // MARK: - Selection State
    override var isSelected: Bool {
        didSet {
            updateSelectionState()
        }
    }
    
    private func updateSelectionState() {
        UIView.animate(withDuration: 0.2) {
            self.selectionOverlay.isHidden = !self.isSelected
            self.checkmarkView.isHidden = !self.isSelected
            
            if self.isSelected {
                self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            } else {
                self.transform = .identity
            }
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()

        // 取消图片请求
        if let requestID = imageRequestID, requestID != PHInvalidImageRequestID {
            PHImageManager.default().cancelImageRequest(requestID)
            imageRequestID = nil
        }

        // 重置UI状态
        imageView.image = nil
        videoIndicator.isHidden = true
        durationLabel.isHidden = true
        durationLabel.text = nil
        selectionOverlay.isHidden = true
        checkmarkView.isHidden = true
        transform = .identity

        asset = nil
        cacheManager = nil
    }
}