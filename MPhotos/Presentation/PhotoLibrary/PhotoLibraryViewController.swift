import UIKit
import PhotosUI
import Combine

class PhotoLibraryViewController: BaseViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = createLayout()
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = UIColor.systemBackground
        cv.delegate = self
        cv.dataSource = self
        cv.translatesAutoresizingMaskIntoConstraints = false
        cv.contentInsetAdjustmentBehavior = .never
        return cv
    }()
    
    private lazy var topToolbar: TopToolbar = {
        let toolbar = TopToolbar()
        toolbar.title = "图库"
        toolbar.translatesAutoresizingMaskIntoConstraints = false
        return toolbar
    }()
    
    // MARK: - Properties
    private var viewModel: PhotoLibraryViewModel!
    private var isSelectMode = false
    
    // MARK: - Constants
    private let toolbarHeight: CGFloat = 100
    private let itemSpacing: CGFloat = 2
    private let itemsPerRow: Int = 3
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupDependencies()

        // 确保 viewModel 已经初始化后再调用其他设置方法
        guard viewModel != nil else {
            logError("ViewModel is nil after setupDependencies")
            return
        }

        setupUI()
        setupBindings()
        setupToolbarActions()
        requestPhotosPermission()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateCollectionViewContentInset()
    }
    
    // MARK: - Setup
    private func setupDependencies() {
        let container = ServiceContainer.shared

        // 验证服务是否已注册
        guard container.isRegistered(MediaManaging.self) && container.isRegistered(CacheManaging.self) else {
            logError("Required services not registered. Attempting to register default services...")
            container.registerDefaultServices()

            // 再次检查
            guard container.isRegistered(MediaManaging.self) && container.isRegistered(CacheManaging.self) else {
                fatalError("Failed to register required services for PhotoLibraryViewController")
            }
        }

        let mediaManager: MediaManaging = container.resolve()
        let cacheManager: CacheManaging = container.resolve()
        viewModel = PhotoLibraryViewModel(
            mediaManager: mediaManager,
            cacheManager: cacheManager
        )
        logInfo("PhotoLibraryViewController dependencies resolved successfully")
    }
    
    override func setupUI() {
        super.setupUI()
        view.backgroundColor = UIColor.systemBackground
        
        // 添加子视图
        view.addSubview(collectionView)
        view.addSubview(topToolbar)
        
        // 注册Cell
        collectionView.register(
            PhotoGridCell.self,
            forCellWithReuseIdentifier: PhotoGridCell.identifier
        )
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // CollectionView 约束 - 填满整个视图
            collectionView.topAnchor.constraint(equalTo: view.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // TopToolbar 约束 - 悬浮在顶部
            topToolbar.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            topToolbar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            topToolbar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            topToolbar.heightAnchor.constraint(equalToConstant: toolbarHeight)
        ])
    }
    
    override func setupBindings() {
        guard let viewModel = viewModel else {
            logError("Cannot setup bindings: viewModel is nil")
            return
        }

        viewModel.$assets
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)

        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                // 可以在这里显示/隐藏加载指示器
            }
            .store(in: &cancellables)

        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.displayError(error)
            }
            .store(in: &cancellables)
    }
    
    private func setupToolbarActions() {
        topToolbar.onSelectTapped = { [weak self] in
            self?.toggleSelectMode()
        }
        
        topToolbar.onMoreTapped = { [weak self] in
            self?.showMoreOptions()
        }
    }
    
    private func updateCollectionViewContentInset() {
        let topInset = topToolbar.frame.maxY
        collectionView.contentInset = UIEdgeInsets(
            top: topInset,
            left: 0,
            bottom: 0,
            right: 0
        )
        collectionView.scrollIndicatorInsets = collectionView.contentInset
    }
    
    // MARK: - Layout
    private func createLayout() -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0/3.0),
            heightDimension: .fractionalHeight(1.0)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        item.contentInsets = NSDirectionalEdgeInsets(
            top: itemSpacing/2,
            leading: itemSpacing/2,
            bottom: itemSpacing/2,
            trailing: itemSpacing/2
        )
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .fractionalWidth(1.0/3.0)
        )
        let group = NSCollectionLayoutGroup.horizontal(
            layoutSize: groupSize,
            subitems: [item]
        )
        
        let section = NSCollectionLayoutSection(group: group)
        section.contentInsets = NSDirectionalEdgeInsets(
            top: itemSpacing/2,
            leading: itemSpacing/2,
            bottom: itemSpacing/2,
            trailing: itemSpacing/2
        )
        
        return UICollectionViewCompositionalLayout(section: section)
    }
    
    // MARK: - Actions
    private func requestPhotosPermission() {
        guard let viewModel = viewModel else {
            logError("Cannot request photos permission: viewModel is nil")
            return
        }
        viewModel.requestPhotosPermission()
    }
    
    private func toggleSelectMode() {
        isSelectMode.toggle()
        topToolbar.setSelectMode(isSelectMode)
        collectionView.allowsMultipleSelection = isSelectMode
        
        // 可以在这里添加选择模式的UI变化
        if !isSelectMode {
            // 清空选择
            collectionView.indexPathsForSelectedItems?.forEach {
                collectionView.deselectItem(at: $0, animated: true)
            }
        }
    }
    
    private func showMoreOptions() {
        let alert = UIAlertController(title: "更多选项", message: nil, preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "刷新", style: .default) { [weak self] _ in
            self?.viewModel.refreshAssets()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = topToolbar
            popover.sourceRect = CGRect(x: topToolbar.bounds.width - 60, y: topToolbar.bounds.height/2, width: 1, height: 1)
        }
        
        present(alert, animated: true)
    }
    
}

// MARK: - UICollectionViewDataSource
extension PhotoLibraryViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let viewModel = viewModel else {
            logError("Cannot get assets count: viewModel is nil")
            return 0
        }
        return viewModel.assets.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(
            withReuseIdentifier: PhotoGridCell.identifier,
            for: indexPath
        ) as? PhotoGridCell else {
            logError("Failed to dequeue PhotoGridCell")
            return UICollectionViewCell()
        }

        guard let viewModel = viewModel else {
            logError("Cannot configure cell: viewModel is nil")
            return cell
        }

        guard indexPath.item < viewModel.assets.count else {
            logError("Index out of bounds: \(indexPath.item) >= \(viewModel.assets.count)")
            return cell
        }

        let asset = viewModel.assets[indexPath.item]
        cell.configure(with: asset, cacheManager: viewModel.cacheManager)

        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PhotoLibraryViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isSelectMode {
            // 选择模式下的处理
            return
        }
        
        // 正常模式下打开图片浏览器
        let asset = viewModel.assets[indexPath.item]
        // TODO: 打开全屏图片浏览器
        print("打开图片: \(asset.localIdentifier)")
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 根据滚动偏移量更新毛玻璃效果
        let offset = scrollView.contentOffset.y + scrollView.contentInset.top
        topToolbar.updateBlurEffect(scrollOffset: max(0, offset))
    }
}