import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBarAppearance()
        setupViewControllers()
    }
    
    private func setupTabBarAppearance() {
        // iOS 17 风格的TabBar样式
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        // 设置选中和未选中状态的颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemBlue
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemBlue
        ]
        
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray
        ]
        
        tabBar.standardAppearance = appearance
        tabBar.scrollEdgeAppearance = appearance
        
        // 设置背景毛玻璃效果
        tabBar.isTranslucent = true
    }
    
    private func setupViewControllers() {
        let photoLibraryVC = createPhotoLibraryViewController()
        let cleanVC = createCleanViewController()
        let albumsVC = createAlbumsViewController()
        let featuresVC = createFeaturesViewController()
        
        viewControllers = [photoLibraryVC, cleanVC, albumsVC, featuresVC]
    }
    
    private func createPhotoLibraryViewController() -> UIViewController {
        let photoLibraryVC = PhotoLibraryViewController()
        let navController = UINavigationController(rootViewController: photoLibraryVC)
        navController.tabBarItem = UITabBarItem(
            title: "图库",
            image: UIImage(systemName: "photo.on.rectangle"),
            selectedImage: UIImage(systemName: "photo.on.rectangle.fill")
        )
        return navController
    }
    
    private func createCleanViewController() -> UIViewController {
        let cleanVC = CleanViewController()
        let navController = UINavigationController(rootViewController: cleanVC)
        navController.tabBarItem = UITabBarItem(
            title: "清理",
            image: UIImage(systemName: "trash"),
            selectedImage: UIImage(systemName: "trash.fill")
        )
        return navController
    }
    
    private func createAlbumsViewController() -> UIViewController {
        let albumsVC = AlbumsViewController()
        let navController = UINavigationController(rootViewController: albumsVC)
        navController.tabBarItem = UITabBarItem(
            title: "相簿",
            image: UIImage(systemName: "rectangle.stack"),
            selectedImage: UIImage(systemName: "rectangle.stack.fill")
        )
        return navController
    }
    
    private func createFeaturesViewController() -> UIViewController {
        let featuresVC = FeaturesViewController()
        let navController = UINavigationController(rootViewController: featuresVC)
        navController.tabBarItem = UITabBarItem(
            title: "功能",
            image: UIImage(systemName: "ellipsis.circle"),
            selectedImage: UIImage(systemName: "ellipsis.circle.fill")
        )
        return navController
    }
}