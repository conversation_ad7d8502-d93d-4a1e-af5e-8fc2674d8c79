import XCTest
@testable import MPhotos

class PhotoLibraryViewControllerTests: XCTestCase {
    
    var viewController: PhotoLibraryViewController!
    
    override func setUp() {
        super.setUp()
        
        // 确保服务已注册
        let container = ServiceContainer.shared
        if !container.isRegistered(MediaManaging.self) {
            container.registerDefaultServices()
        }
        
        viewController = PhotoLibraryViewController()
    }
    
    override func tearDown() {
        viewController = nil
        super.tearDown()
    }
    
    func testViewControllerInitialization() {
        // 测试视图控制器能够正常初始化
        XCTAssertNotNil(viewController)
    }
    
    func testViewDidLoadDoesNotCrash() {
        // 测试 viewDidLoad 不会崩溃
        XCTAssertNoThrow {
            _ = viewController.view // 触发 viewDidLoad
        }
    }
    
    func testServiceContainerRegistration() {
        // 测试服务容器中的服务已正确注册
        let container = ServiceContainer.shared
        XCTAssertTrue(container.isRegistered(MediaManaging.self))
        XCTAssertTrue(container.isRegistered(CacheManaging.self))
    }
    
    func testDependencyResolution() {
        // 测试依赖注入能够正常工作
        XCTAssertNoThrow {
            let container = ServiceContainer.shared
            let _: MediaManaging = container.resolve()
            let _: CacheManaging = container.resolve()
        }
    }
}
