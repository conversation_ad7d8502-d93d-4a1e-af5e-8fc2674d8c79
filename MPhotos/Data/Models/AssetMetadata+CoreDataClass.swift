//
//  AssetMetadata+CoreDataClass.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

@objc(AssetMetadata)
public class AssetMetadata: NSManagedObject {
    
    // MARK: - Convenience Initializers
    convenience init(assetId: String, context: NSManagedObjectContext) {
        self.init(context: context)
        self.assetId = assetId
        self.createdDate = Date()
        self.lastViewedDate = Date()
        self.viewCount = 0
        self.isEdited = false
    }
    
    // MARK: - Helper Methods
    func incrementViewCount() {
        viewCount += 1
        lastViewedDate = Date()
    }
    
    func setEditedStatus(_ edited: Bool) {
        isEdited = edited
        if edited && editedDate == nil {
            editedDate = Date()
        }
    }
    
    func updateExtendedMetadata(_ metadata: [String: Any]) {
        do {
            let data = try JSONSerialization.data(withJSONObject: metadata)
            extendedMetadata = data
        } catch {
            logError("Failed to serialize extended metadata: \(error)")
        }
    }
    
    func getExtendedMetadata() -> [String: Any]? {
        guard let data = extendedMetadata else { return nil }
        
        do {
            return try JSONSerialization.jsonObject(with: data) as? [String: Any]
        } catch {
            logError("Failed to deserialize extended metadata: \(error)")
            return nil
        }
    }
}