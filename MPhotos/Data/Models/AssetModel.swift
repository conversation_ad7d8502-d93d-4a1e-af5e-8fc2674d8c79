//
//  AssetModel.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Photos
import UIKit
import CoreLocation
import AVFoundation

struct AssetModel {
    let asset: PHAsset
    let identifier: String
    let mediaType: PHAssetMediaType
    let creationDate: Date?
    let modificationDate: Date?
    let duration: TimeInterval
    let pixelWidth: Int
    let pixelHeight: Int
    let isFavorite: Bool
    let isHidden: Bool
    let location: CLLocation?
    
    init(asset: PHAsset) {
        self.asset = asset
        self.identifier = asset.localIdentifier
        self.mediaType = asset.mediaType
        self.creationDate = asset.creationDate
        self.modificationDate = asset.modificationDate
        self.duration = asset.duration
        self.pixelWidth = asset.pixelWidth
        self.pixelHeight = asset.pixelHeight
        self.isFavorite = asset.isFavorite
        self.isHidden = asset.isHidden
        self.location = asset.location
    }
    
    // MARK: - Computed Properties
    var isVideo: Bool {
        return mediaType == .video
    }
    
    var isImage: Bool {
        return mediaType == .image
    }
    
    var aspectRatio: CGFloat {
        guard pixelHeight > 0 else { return 1.0 }
        return CGFloat(pixelWidth) / CGFloat(pixelHeight)
    }
    
    var durationString: String? {
        guard isVideo else { return nil }
        return asset.durationString
    }
    
    var formattedCreationDate: String? {
        return creationDate?.formatted(style: .medium)
    }
    
    var timeAgo: String? {
        return creationDate?.timeAgo()
    }
    
    var fileSize: Int64 {
        return asset.fileSize
    }
    
    var isInCloud: Bool {
        return asset.isInCloud
    }
    
    // MARK: - Image Loading
    func thumbnail(size: CGSize = Constants.UI.thumbnailSize, completion: @escaping (UIImage?) -> Void) {
        asset.thumbnail(size: size, completion: completion)
    }
    
    @discardableResult
    func fullResolutionImage(completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        return asset.requestFullResolutionImage(completion: completion)
    }
    
    // MARK: - Video Operations
    func requestAVAsset(completion: @escaping (AVAsset?) -> Void) {
        guard isVideo else {
            completion(nil)
            return
        }
        
        let options = PHVideoRequestOptions()
        options.deliveryMode = .automatic
        options.isNetworkAccessAllowed = true
        
        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
            DispatchQueue.main.async {
                completion(avAsset)
            }
        }
    }
    
    // MARK: - Export Operations
    func exportImage(completion: @escaping (Result<UIImage, Error>) -> Void) {
        guard isImage else {
            completion(.failure(MPhotosError.custom("不是图片资源")))
            return
        }
        
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        
        PHImageManager.default().requestImage(
            for: asset,
            targetSize: PHImageManagerMaximumSize,
            contentMode: .aspectFit,
            options: options
        ) { image, info in
            DispatchQueue.main.async {
                if let error = info?[PHImageErrorKey] as? Error {
                    completion(.failure(error))
                } else if let image = image {
                    completion(.success(image))
                } else {
                    completion(.failure(MPhotosError.exportFailed("无法获取图片")))
                }
            }
        }
    }
    
    func exportVideo(to url: URL, completion: @escaping (Result<URL, Error>) -> Void) {
        guard isVideo else {
            completion(.failure(MPhotosError.custom("不是视频资源")))
            return
        }
        
        let options = PHVideoRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        
        PHImageManager.default().requestExportSession(
            forVideo: asset,
            options: options,
            exportPreset: AVAssetExportPresetHighestQuality
        ) { exportSession, _ in
            guard let exportSession = exportSession else {
                completion(.failure(MPhotosError.exportFailed("无法创建导出会话")))
                return
            }
            
            exportSession.outputURL = url
            exportSession.outputFileType = .mp4
            
            exportSession.exportAsynchronously {
                DispatchQueue.main.async {
                    switch exportSession.status {
                    case .completed:
                        completion(.success(url))
                    case .failed:
                        let error = exportSession.error ?? MPhotosError.exportFailed("导出失败")
                        completion(.failure(error))
                    case .cancelled:
                        completion(.failure(MPhotosError.custom("导出已取消")))
                    default:
                        completion(.failure(MPhotosError.exportFailed("导出状态异常")))
                    }
                }
            }
        }
    }
}

// MARK: - Hashable & Equatable
extension AssetModel: Hashable, Equatable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(identifier)
    }
    
    static func == (lhs: AssetModel, rhs: AssetModel) -> Bool {
        return lhs.identifier == rhs.identifier
    }
}

// MARK: - Identifiable
extension AssetModel: Identifiable {
    var id: String { identifier }
}