//
//  EditHistory+CoreDataProperties.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

extension EditHistory {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<EditHistory> {
        return NSFetchRequest<EditHistory>(entityName: "EditHistory")
    }

    @NSManaged public var historyId: String
    @NSManaged public var assetId: String
    @NSManaged public var editDate: Date?
    @NSManaged public var editType: String
    @NSManaged public var adjustments: Data?
    @NSManaged public var previewData: Data?
    @NSManaged public var isCurrentVersion: Bool
    @NSManaged public var assetMetadata: AssetMetadata?

}

extension EditHistory : Identifiable {

}