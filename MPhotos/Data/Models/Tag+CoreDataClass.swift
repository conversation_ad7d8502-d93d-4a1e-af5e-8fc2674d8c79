//
//  Tag+CoreDataClass.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

@objc(Tag)
public class Tag: NSManagedObject {
    
    // MARK: - Convenience Initializers
    convenience init(name: String, category: String? = nil, context: NSManagedObjectContext) {
        self.init(context: context)
        self.tagId = UUID().uuidString
        self.name = name
        self.category = category
        self.createdDate = Date()
        self.usageCount = 0
        self.color = "#007AFF" // Default blue color
    }
    
    // MARK: - Helper Methods
    func incrementUsage() {
        usageCount += 1
    }
    
    func decrementUsage() {
        if usageCount > 0 {
            usageCount -= 1
        }
    }
    
    func setColor(hex: String) {
        // Validate hex color format
        let cleanHex = hex.hasPrefix("#") ? hex : "#\(hex)"
        if cleanHex.count == 7 {
            color = cleanHex
        } else {
            logWarning("Invalid hex color format: \(hex)")
        }
    }
    
    var assetArray: [AssetMetadata] {
        return assets?.allObjects as? [AssetMetadata] ?? []
    }
    
    var isUnused: Bool {
        return usageCount == 0 && (assets?.count ?? 0) == 0
    }
}