//
//  Tag+CoreDataProperties.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

extension Tag {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Tag> {
        return NSFetchRequest<Tag>(entityName: "Tag")
    }

    @NSManaged public var tagId: String
    @NSManaged public var name: String
    @NSManaged public var category: String?
    @NSManaged public var usageCount: Int32
    @NSManaged public var createdDate: Date?
    @NSManaged public var color: String
    @NSManaged public var assets: NSSet?

}

// MARK: Generated accessors for assets
extension Tag {

    @objc(addAssetsObject:)
    @NSManaged public func addToAssets(_ value: AssetMetadata)

    @objc(removeAssetsObject:)
    @NSManaged public func removeFromAssets(_ value: AssetMetadata)

    @objc(addAssets:)
    @NSManaged public func addToAssets(_ values: NSSet)

    @objc(removeAssets:)
    @NSManaged public func removeFromAssets(_ values: NSSet)

}

extension Tag : Identifiable {

}