//
//  EditHistory+CoreDataClass.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

@objc(EditHistory)
public class EditHistory: NSManagedObject {
    
    // MARK: - Convenience Initializers
    convenience init(assetId: String, editType: String, context: NSManagedObjectContext) {
        self.init(context: context)
        self.historyId = UUID().uuidString
        self.assetId = assetId
        self.editType = editType
        self.editDate = Date()
        self.isCurrentVersion = true
    }
    
    // MARK: - Helper Methods
    func setAdjustments(_ adjustments: [String: Any]) {
        do {
            let data = try JSONSerialization.data(withJSONObject: adjustments)
            self.adjustments = data
        } catch {
            logError("Failed to serialize adjustments: \(error)")
        }
    }
    
    func getAdjustments() -> [String: Any]? {
        guard let data = adjustments else { return nil }
        
        do {
            return try JSONSerialization.jsonObject(with: data) as? [String: Any]
        } catch {
            logError("Failed to deserialize adjustments: \(error)")
            return nil
        }
    }
    
    func markAsCurrentVersion() {
        // Mark other versions as non-current
        let context = managedObjectContext!
        let request: NSFetchRequest<EditHistory> = EditHistory.fetchRequest()
        request.predicate = NSPredicate(format: "assetId == %@ AND historyId != %@", assetId, historyId)
        
        do {
            let otherVersions = try context.fetch(request)
            otherVersions.forEach { $0.isCurrentVersion = false }
            self.isCurrentVersion = true
        } catch {
            logError("Failed to update current version: \(error)")
        }
    }
}