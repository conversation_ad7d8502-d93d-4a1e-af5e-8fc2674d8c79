//
//  AssetMetadata+CoreDataProperties.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

extension AssetMetadata {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<AssetMetadata> {
        return NSFetchRequest<AssetMetadata>(entityName: "AssetMetadata")
    }

    @NSManaged public var assetId: String
    @NSManaged public var userDescription: String?
    @NSManaged public var viewCount: Int32
    @NSManaged public var lastViewedDate: Date?
    @NSManaged public var isEdited: Bool
    @NSManaged public var originalChecksum: String?
    @NSManaged public var fileSize: Int64
    @NSManaged public var extendedMetadata: Data?
    @NSManaged public var createdDate: Date?
    @NSManaged public var editedDate: Date?
    @NSManaged public var editHistories: NSSet?
    @NSManaged public var tags: NSSet?

}

// MARK: Generated accessors for editHistories
extension AssetMetadata {

    @objc(addEditHistoriesObject:)
    @NSManaged public func addToEditHistories(_ value: EditHistory)

    @objc(removeEditHistoriesObject:)
    @NSManaged public func removeFromEditHistories(_ value: EditHistory)

    @objc(addEditHistories:)
    @NSManaged public func addToEditHistories(_ values: NSSet)

    @objc(removeEditHistories:)
    @NSManaged public func removeFromEditHistories(_ values: NSSet)

}

// MARK: Generated accessors for tags
extension AssetMetadata {

    @objc(addTagsObject:)
    @NSManaged public func addToTags(_ value: Tag)

    @objc(removeTagsObject:)
    @NSManaged public func removeFromTags(_ value: Tag)

    @objc(addTags:)
    @NSManaged public func addToTags(_ values: NSSet)

    @objc(removeTags:)
    @NSManaged public func removeFromTags(_ values: NSSet)

}

extension AssetMetadata : Identifiable {

}