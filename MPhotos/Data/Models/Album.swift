//
//  Album.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Photos
import UIKit

enum AlbumType {
    case smartAlbum
    case userAlbum
    case sharedAlbum
    case moment
    
    var localizedTitle: String {
        switch self {
        case .smartAlbum:
            return "智能相册"
        case .userAlbum:
            return "用户相册"
        case .sharedAlbum:
            return "共享相册"
        case .moment:
            return "时刻"
        }
    }
}

struct Album {
    let identifier: String
    let title: String
    let type: AlbumType
    let assetCount: Int
    let keyAsset: PHAsset?
    let collection: PHAssetCollection?
    
    var localizedTitle: String {
        return title.isEmpty ? type.localizedTitle : title
    }
    
    var isEmpty: Bool {
        return assetCount == 0
    }
    
    init(
        identifier: String,
        title: String,
        type: AlbumType,
        assetCount: Int = 0,
        keyAsset: PHAsset? = nil,
        collection: PHAssetCollection? = nil
    ) {
        self.identifier = identifier
        self.title = title
        self.type = type
        self.assetCount = assetCount
        self.keyAsset = keyAsset
        self.collection = collection
    }
    
    // MARK: - Factory Methods
    static func from(_ collection: PHAssetCollection) -> Album {
        let type: AlbumType
        switch collection.assetCollectionType {
        case .smartAlbum:
            type = .smartAlbum
        case .album:
            type = .userAlbum
        case .moment:
            type = .moment
        @unknown default:
            type = .userAlbum
        }
        
        let assets = PHAsset.fetchAssets(in: collection, options: nil)
        
        return Album(
            identifier: collection.localIdentifier,
            title: collection.localizedTitle ?? "",
            type: type,
            assetCount: assets.count,
            keyAsset: assets.firstObject,
            collection: collection
        )
    }
    
    // MARK: - Asset Operations
    func fetchAssets(sortBy descriptors: [NSSortDescriptor]? = nil) -> PHFetchResult<PHAsset> {
        guard let collection = collection else {
            return PHFetchResult()
        }
        
        let options = PHFetchOptions()
        options.sortDescriptors = descriptors ?? [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options)
    }
    
    func getCoverImage(size: CGSize = Constants.UI.thumbnailSize, completion: @escaping (UIImage?) -> Void) {
        guard let keyAsset = keyAsset else {
            completion(nil)
            return
        }
        
        keyAsset.thumbnail(size: size, completion: completion)
    }
}

// MARK: - Hashable & Equatable
extension Album: Hashable, Equatable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(identifier)
    }
    
    static func == (lhs: Album, rhs: Album) -> Bool {
        return lhs.identifier == rhs.identifier
    }
}

// MARK: - Identifiable
extension Album: Identifiable {
    var id: String { identifier }
}