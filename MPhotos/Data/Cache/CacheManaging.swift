//
//  CacheManaging.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import UIKit
import Photos

// MARK: - Cache Management Protocol
protocol CacheManaging {
    // MARK: - Image Caching
    func store(_ image: UIImage, for key: String) async
    func image(for key: String) async -> UIImage?
    func removeImage(for key: String) async
    func clearAll() async
    
    // MARK: - PHAsset Image Requests
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode) async -> UIImage?
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID
    
    // MARK: - Preloading
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize)
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize)
    func stopAllPreloading()
    
    // MARK: - Cache Information
    var currentMemoryUsage: Int { get }
    var cacheCount: Int { get }
    var hitRate: Double { get }
    
    // MARK: - Cache Configuration
    func setCacheLimit(_ limit: Int)
    func setCountLimit(_ limit: Int)
}

// MARK: - Cache Key Generation
extension CacheManaging {
    func cacheKey(for asset: PHAsset, size: CGSize, contentMode: PHImageContentMode = .aspectFill) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))_\(contentMode.rawValue)"
    }
}