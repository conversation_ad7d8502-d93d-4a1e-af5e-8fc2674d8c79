//
//  CacheManager.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import UIKit
import Photos

final class CacheManager: NSObject, CacheManaging {
    
    // MARK: - Singleton
    static let shared = CacheManager()
    
    // MARK: - Private Properties
    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    private let queue = DispatchQueue(label: "cacheManager", qos: .userInitiated, attributes: .concurrent)
    
    // Statistics
    private var _hitCount: Int = 0
    private var _missCount: Int = 0
    private let statisticsQueue = DispatchQueue(label: "cacheStatistics", qos: .utility)
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupMemoryCache()
        setupImageManager()
        observeMemoryWarnings()
        logInfo("CacheManager initialized")
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        logInfo("CacheManager deinitialized")
    }
    
    // MARK: - Setup
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = Constants.Cache.memoryCacheLimit
        memoryCache.countLimit = Constants.Cache.memoryCacheCountLimit
        memoryCache.delegate = self
    }
    
    private func setupImageManager() {
        imageManager.allowsCachingHighQualityImages = true
    }
    
    private func observeMemoryWarnings() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(didReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func didReceiveMemoryWarning() {
        logWarning("Memory warning received, clearing cache")
        Task {
            await clearMemoryCache()
        }
    }
    
    // MARK: - Memory Cache Operations
    func store(_ image: UIImage, for key: String) async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                guard let self = self else { 
                    continuation.resume()
                    return 
                }
                
                let cost = self.calculateImageCost(image)
                self.memoryCache.setObject(image, forKey: key as NSString, cost: cost)
                logDebug("Stored image in cache with key: \(key)")
                continuation.resume()
            }
        }
    }
    
    func image(for key: String) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            queue.async { [weak self] in
                guard let self else { 
                    continuation.resume(returning: nil)
                    return 
                }
                
                let image = self.memoryCache.object(forKey: key as NSString)
                
                self.statisticsQueue.async { [weak self] in
                    if image != nil {
                        self?._hitCount += 1
                    } else {
                        self?._missCount += 1
                    }
                }
                
                if image != nil {
                    logDebug("Cache hit for key: \(key)")
                } else {
                    logDebug("Cache miss for key: \(key)")
                }
                
                continuation.resume(returning: image)
            }
        }
    }
    
    func removeImage(for key: String) async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                self?.memoryCache.removeObject(forKey: key as NSString)
                logDebug("Removed image from cache with key: \(key)")
                continuation.resume()
            }
        }
    }
    
    func clearAll() async {
        await clearMemoryCache()
        stopAllPreloading()
        logInfo("Cleared all cache")
    }
    
    private func clearMemoryCache() async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                self?.memoryCache.removeAllObjects()
                logInfo("Cleared memory cache")
                continuation.resume()
            }
        }
    }
    
    // MARK: - PHAsset Image Requests
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode = .aspectFill) async -> UIImage? {
        let key = cacheKey(for: asset, size: targetSize, contentMode: contentMode)
        
        // Check memory cache first
        if let cachedImage = await image(for: key) {
            return cachedImage
        }
        
        // Request from PHImageManager
        return await withCheckedContinuation { continuation in
            let options = createImageRequestOptions()
            
            imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: contentMode,
                options: options
            ) { [weak self] image, info in
                guard let image = image else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // Check if this is the final high-quality image
                let isDegraded = info?[PHImageResultIsDegradedKey] as? Bool ?? false
                if !isDegraded {
                    // Store in memory cache
                    Task { [weak self] in
                        await self?.store(image, for: key)
                    }
                }
                
                continuation.resume(returning: image)
            }
        }
    }
    
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        let key = cacheKey(for: asset, size: targetSize)
        
        // Check memory cache first
        Task { [weak self] in
            if let cachedImage = await self?.image(for: key) {
                DispatchQueue.main.async {
                    completion(cachedImage)
                }
                return
            }
            
            // Request from PHImageManager
            let options = self?.createImageRequestOptions()
            let requestID = self?.imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            ) { [weak self] image, info in
                guard let image = image else {
                    DispatchQueue.main.async { completion(nil) }
                    return
                }
                
                // Check if this is the final high-quality image
                let isDegraded = info?[PHImageResultIsDegradedKey] as? Bool ?? false
                if !isDegraded {
                    // Store in memory cache
                    Task { [weak self] in
                        await self?.store(image, for: key)
                    }
                }
                
                DispatchQueue.main.async {
                    completion(image)
                }
            }
        }
        
        return 0 // Return placeholder ID, actual implementation would return real ID
    }
    
    private func createImageRequestOptions() -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false // First try local only
        options.isSynchronous = false
        return options
    }
    
    // MARK: - Preloading
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize) {
        guard !assets.isEmpty else { return }
        
        let options = createImageRequestOptions()
        imageManager.startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )
        
        logDebug("Started preloading \(assets.count) assets")
    }
    
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize) {
        guard !assets.isEmpty else { return }
        
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
        
        logDebug("Stopped preloading \(assets.count) assets")
    }
    
    func stopAllPreloading() {
        imageManager.stopCachingImagesForAllAssets()
        logDebug("Stopped all preloading")
    }
    
    // MARK: - Cache Information
    var currentMemoryUsage: Int {
        return memoryCache.totalCostLimit
    }
    
    var cacheCount: Int {
        var count = 0
        queue.sync {
            // NSCache doesn't provide count directly, this is an approximation
            count = memoryCache.countLimit
        }
        return count
    }
    
    var hitRate: Double {
        return statisticsQueue.sync {
            let total = _hitCount + _missCount
            guard total > 0 else { return 0.0 }
            return Double(_hitCount) / Double(total)
        }
    }
    
    // MARK: - Cache Configuration
    func setCacheLimit(_ limit: Int) {
        queue.async(flags: .barrier) { [weak self] in
            self?.memoryCache.totalCostLimit = limit
            logInfo("Set cache limit to \(limit) bytes")
        }
    }
    
    func setCountLimit(_ limit: Int) {
        queue.async(flags: .barrier) { [weak self] in
            self?.memoryCache.countLimit = limit
            logInfo("Set cache count limit to \(limit)")
        }
    }
    
    // MARK: - Helpers
    private func calculateImageCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
        return pixelCount * 4 // 4 bytes per pixel (RGBA)
    }
}

// MARK: - Memory Management
extension CacheManager {
    func handleMemoryWarning() {
        Task {
            await clearMemoryCache()
        }
        
        // Also clear system cache
        stopAllPreloading()
        
        NotificationCenter.default.post(
            name: Constants.Notifications.cacheDidClear,
            object: self
        )
    }
}

// MARK: - NSCacheDelegate
extension CacheManager: NSCacheDelegate {
    nonisolated func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        logDebug("Cache will evict object of type: \(type(of: obj))")
    }
}