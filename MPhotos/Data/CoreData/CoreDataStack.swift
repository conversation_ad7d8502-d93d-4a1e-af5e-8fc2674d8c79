//
//  CoreDataStack.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData

final class CoreDataStack {
    
    // MARK: - Singleton
    static let shared = CoreDataStack()
    
    // MARK: - Core Data stack
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "MPhotos")
        container.loadPersistentStores(completionHandler: { [weak self] (storeDescription, error) in
            if let error = error as NSError? {
                logError("Core Data failed to load store: \(error), \(error.userInfo)")
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
            logInfo("Core Data store loaded successfully")
        })
        
        // Configure for performance
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyStoreTrumpMergePolicy
        
        return container
    }()
    
    // MARK: - Contexts
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    var backgroundContext: NSManagedObjectContext {
        return persistentContainer.newBackgroundContext()
    }
    
    // MARK: - Initialization
    private init() {
        logInfo("CoreDataStack initialized")
    }
    
    // MARK: - Save Context
    func saveContext() {
        let context = persistentContainer.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
                logDebug("Core Data context saved successfully")
            } catch {
                logError("Failed to save Core Data context: \(error)")
                let nsError = error as NSError
                fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    func saveBackgroundContext(_ context: NSManagedObjectContext) {
        context.perform {
            if context.hasChanges {
                do {
                    try context.save()
                    logDebug("Background context saved successfully")
                } catch {
                    logError("Failed to save background context: \(error)")
                }
            }
        }
    }
    
    // MARK: - Background Operations
    func performBackgroundTask(_ block: @escaping (NSManagedObjectContext) -> Void) {
        persistentContainer.performBackgroundTask { context in
            context.mergePolicy = NSMergeByPropertyStoreTrumpMergePolicy
            block(context)
        }
    }
    
    // MARK: - Entity Management
    func createEntity<T: NSManagedObject>(_ entityType: T.Type, in context: NSManagedObjectContext? = nil) -> T {
        let context = context ?? viewContext
        let entityName = String(describing: entityType)
        
        guard let entity = NSEntityDescription.entity(forEntityName: entityName, in: context) else {
            fatalError("Failed to find entity description for \(entityName)")
        }
        
        return T(entity: entity, insertInto: context)
    }
    
    func fetchEntities<T: NSManagedObject>(_ entityType: T.Type, 
                                         predicate: NSPredicate? = nil,
                                         sortDescriptors: [NSSortDescriptor]? = nil,
                                         fetchLimit: Int? = nil,
                                         context: NSManagedObjectContext? = nil) -> [T] {
        let context = context ?? viewContext
        let request = NSFetchRequest<T>(entityName: String(describing: entityType))
        
        request.predicate = predicate
        request.sortDescriptors = sortDescriptors
        
        if let fetchLimit = fetchLimit {
            request.fetchLimit = fetchLimit
        }
        
        do {
            return try context.fetch(request)
        } catch {
            logError("Failed to fetch \(entityType): \(error)")
            return []
        }
    }
    
    func deleteEntity(_ entity: NSManagedObject, context: NSManagedObjectContext? = nil) {
        let context = context ?? viewContext
        context.delete(entity)
    }
    
    func deleteEntities<T: NSManagedObject>(_ entityType: T.Type, 
                                          predicate: NSPredicate? = nil,
                                          context: NSManagedObjectContext? = nil) {
        let context = context ?? viewContext
        let request = NSFetchRequest<T>(entityName: String(describing: entityType))
        request.predicate = predicate
        
        do {
            let entities = try context.fetch(request)
            entities.forEach { context.delete($0) }
            logInfo("Deleted \(entities.count) \(entityType) entities")
        } catch {
            logError("Failed to delete \(entityType) entities: \(error)")
        }
    }
}

// MARK: - Test Support
extension CoreDataStack {
    static var test: CoreDataStack {
        let stack = CoreDataStack()
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        stack.persistentContainer.persistentStoreDescriptions = [description]
        
        let container = stack.persistentContainer
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Failed to load test store: \(error)")
            }
        }
        
        return stack
    }
}