//
//  MetadataRepository.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import CoreData
import Photos

protocol MetadataRepositoryProtocol {
    // MARK: - Asset Metadata
    func getAssetMetadata(for assetId: String) async -> AssetMetadata?
    func createAssetMetadata(for assetId: String) async -> AssetMetadata
    func updateAssetMetadata(_ metadata: AssetMetadata) async throws
    func deleteAssetMetadata(for assetId: String) async throws
    
    // MARK: - Edit History
    func getEditHistory(for assetId: String) async -> [EditHistory]
    func addEditHistory(assetId: String, editType: String, adjustments: [String: Any]) async -> EditHistory
    func getCurrentVersion(for assetId: String) async -> EditHistory?
    
    // MARK: - Tags
    func getAllTags() async -> [Tag]
    func createTag(name: String, category: String?) async -> Tag
    func getTag(by name: String) async -> Tag?
    func deleteTag(_ tag: Tag) async throws
    func addTag(_ tag: Tag, to assetId: String) async throws
    func removeTag(_ tag: Tag, from assetId: String) async throws
    
    // MARK: - Statistics
    func getViewStatistics(for assetId: String) async -> (viewCount: Int, lastViewed: Date?)
    func recordView(for assetId: String) async
}

final class MetadataRepository: MetadataRepositoryProtocol {
    
    private let coreDataStack: CoreDataStack
    
    init(coreDataStack: CoreDataStack = CoreDataStack.shared) {
        self.coreDataStack = coreDataStack
    }
    
    // MARK: - Asset Metadata
    func getAssetMetadata(for assetId: String) async -> AssetMetadata? {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                request.fetchLimit = 1
                
                do {
                    let results = try context.fetch(request)
                    continuation.resume(returning: results.first)
                } catch {
                    logError("Failed to fetch asset metadata: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    func createAssetMetadata(for assetId: String) async -> AssetMetadata {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let metadata = AssetMetadata(assetId: assetId, context: context)
                
                do {
                    try context.save()
                    logDebug("Created asset metadata for: \(assetId)")
                    continuation.resume(returning: metadata)
                } catch {
                    logError("Failed to create asset metadata: \(error)")
                    // Return the metadata even if save failed, let caller handle retry
                    continuation.resume(returning: metadata)
                }
            }
        }
    }
    
    func updateAssetMetadata(_ metadata: AssetMetadata) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            guard let context = metadata.managedObjectContext else {
                continuation.resume(throwing: MPhotosError.custom("Metadata has no managed object context"))
                return
            }
            
            context.perform {
                do {
                    try context.save()
                    logDebug("Updated asset metadata for: \(metadata.assetId)")
                    continuation.resume()
                } catch {
                    logError("Failed to update asset metadata: \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func deleteAssetMetadata(for assetId: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                
                do {
                    let results = try context.fetch(request)
                    results.forEach { context.delete($0) }
                    try context.save()
                    logDebug("Deleted asset metadata for: \(assetId)")
                    continuation.resume()
                } catch {
                    logError("Failed to delete asset metadata: \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Edit History
    func getEditHistory(for assetId: String) async -> [EditHistory] {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<EditHistory> = EditHistory.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                request.sortDescriptors = [NSSortDescriptor(key: "editDate", ascending: false)]
                
                do {
                    let results = try context.fetch(request)
                    continuation.resume(returning: results)
                } catch {
                    logError("Failed to fetch edit history: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    func addEditHistory(assetId: String, editType: String, adjustments: [String: Any]) async -> EditHistory {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let history = EditHistory(assetId: assetId, editType: editType, context: context)
                history.setAdjustments(adjustments)
                history.markAsCurrentVersion()
                
                do {
                    try context.save()
                    logDebug("Added edit history for asset: \(assetId)")
                    continuation.resume(returning: history)
                } catch {
                    logError("Failed to add edit history: \(error)")
                    continuation.resume(returning: history)
                }
            }
        }
    }
    
    func getCurrentVersion(for assetId: String) async -> EditHistory? {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<EditHistory> = EditHistory.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@ AND isCurrentVersion == YES", assetId)
                request.fetchLimit = 1
                
                do {
                    let results = try context.fetch(request)
                    continuation.resume(returning: results.first)
                } catch {
                    logError("Failed to fetch current version: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    // MARK: - Tags
    func getAllTags() async -> [Tag] {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<Tag> = Tag.fetchRequest()
                request.sortDescriptors = [
                    NSSortDescriptor(key: "usageCount", ascending: false),
                    NSSortDescriptor(key: "name", ascending: true)
                ]
                
                do {
                    let results = try context.fetch(request)
                    continuation.resume(returning: results)
                } catch {
                    logError("Failed to fetch tags: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    func createTag(name: String, category: String? = nil) async -> Tag {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let tag = Tag(name: name, category: category, context: context)
                
                do {
                    try context.save()
                    logDebug("Created tag: \(name)")
                    continuation.resume(returning: tag)
                } catch {
                    logError("Failed to create tag: \(error)")
                    continuation.resume(returning: tag)
                }
            }
        }
    }
    
    func getTag(by name: String) async -> Tag? {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<Tag> = Tag.fetchRequest()
                request.predicate = NSPredicate(format: "name == %@", name)
                request.fetchLimit = 1
                
                do {
                    let results = try context.fetch(request)
                    continuation.resume(returning: results.first)
                } catch {
                    logError("Failed to fetch tag by name: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    func deleteTag(_ tag: Tag) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            guard let context = tag.managedObjectContext else {
                continuation.resume(throwing: MPhotosError.custom("Tag has no managed object context"))
                return
            }
            
            context.perform {
                do {
                    context.delete(tag)
                    try context.save()
                    logDebug("Deleted tag: \(tag.name)")
                    continuation.resume()
                } catch {
                    logError("Failed to delete tag: \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func addTag(_ tag: Tag, to assetId: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            coreDataStack.performBackgroundTask { context in
                // Get or create asset metadata
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                
                do {
                    var metadata: AssetMetadata
                    let results = try context.fetch(request)
                    
                    if let existingMetadata = results.first {
                        metadata = existingMetadata
                    } else {
                        metadata = AssetMetadata(assetId: assetId, context: context)
                    }
                    
                    // Add tag to metadata
                    metadata.addToTags(tag)
                    tag.incrementUsage()
                    
                    try context.save()
                    logDebug("Added tag '\(tag.name)' to asset: \(assetId)")
                    continuation.resume()
                } catch {
                    logError("Failed to add tag to asset: \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func removeTag(_ tag: Tag, from assetId: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                
                do {
                    let results = try context.fetch(request)
                    guard let metadata = results.first else {
                        continuation.resume()
                        return
                    }
                    
                    metadata.removeFromTags(tag)
                    tag.decrementUsage()
                    
                    try context.save()
                    logDebug("Removed tag '\(tag.name)' from asset: \(assetId)")
                    continuation.resume()
                } catch {
                    logError("Failed to remove tag from asset: \(error)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Statistics
    func getViewStatistics(for assetId: String) async -> (viewCount: Int, lastViewed: Date?) {
        return await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                request.fetchLimit = 1
                
                do {
                    let results = try context.fetch(request)
                    if let metadata = results.first {
                        continuation.resume(returning: (Int(metadata.viewCount), metadata.lastViewedDate))
                    } else {
                        continuation.resume(returning: (0, nil))
                    }
                } catch {
                    logError("Failed to fetch view statistics: \(error)")
                    continuation.resume(returning: (0, nil))
                }
            }
        }
    }
    
    func recordView(for assetId: String) async {
        await withCheckedContinuation { continuation in
            coreDataStack.performBackgroundTask { context in
                let request: NSFetchRequest<AssetMetadata> = AssetMetadata.fetchRequest()
                request.predicate = NSPredicate(format: "assetId == %@", assetId)
                
                do {
                    var metadata: AssetMetadata
                    let results = try context.fetch(request)
                    
                    if let existingMetadata = results.first {
                        metadata = existingMetadata
                    } else {
                        metadata = AssetMetadata(assetId: assetId, context: context)
                    }
                    
                    metadata.incrementViewCount()
                    try context.save()
                    
                    logDebug("Recorded view for asset: \(assetId)")
                    continuation.resume()
                } catch {
                    logError("Failed to record view: \(error)")
                    continuation.resume()
                }
            }
        }
    }
}