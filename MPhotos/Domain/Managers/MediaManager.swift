//
//  MediaManager.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Photos
import Combine

final class MediaManager: NSObject, MediaManaging {
    
    // MARK: - Singleton
    static let shared = MediaManager()
    
    // MARK: - Sort Descriptors
    static var defaultSortDescriptors: [NSSortDescriptor] {
        return [NSSortDescriptor(key: "creationDate", ascending: false)]
    }
    
    // MARK: - Private Properties
    private let photoLibrary = PHPhotoLibrary.shared()
    private var observers: [WeakMediaObserver] = []
    private let authorizationSubject = CurrentValueSubject<PHAuthorizationStatus, Never>(PHPhotoLibrary.authorizationStatus(for: .addOnly))
    private let photoLibraryChangeSubject = PassthroughSubject<PHChange, Never>()
    
    // MARK: - Publishers
    var authorizationStatusPublisher: AnyPublisher<PHAuthorizationStatus, Never> {
        authorizationSubject.eraseToAnyPublisher()
    }
    
    var photoLibraryChangePublisher: AnyPublisher<PHChange, Never> {
        photoLibraryChangeSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupPhotoLibraryObserver()
        logInfo("MediaManager initialized")
    }
    
    deinit {
        PHPhotoLibrary.shared().unregisterChangeObserver(self)
        logInfo("MediaManager deinitialized")
    }
    
    // MARK: - Setup
    private func setupPhotoLibraryObserver() {
        photoLibrary.register(self)
    }
    
    // MARK: - Authorization
    func requestAuthorization() async throws -> PHAuthorizationStatus {
        logInfo("Requesting photo library authorization")
        
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization(for: .addOnly) { [weak self] status in
                self?.authorizationSubject.send(status)
                self?.notifyObserversAuthorizationChanged(status)
                continuation.resume(returning: status)
            }
        }
    }
    
    func checkAuthorizationStatus() -> PHAuthorizationStatus {
        return PHPhotoLibrary.authorizationStatus(for: .addOnly)
    }
    
    // MARK: - Albums
    func fetchAlbums(ofType type: AlbumType?) async throws -> [Album] {
        let status = checkAuthorizationStatus()
        guard status.isAuthorized else {
            throw MPhotosError.fromPHAuthorizationStatus(status)
        }
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var albums: [Album] = []
                
                // Fetch smart albums
                if type == nil || type == .smartAlbum {
                    let smartAlbums = PHAssetCollection.fetchAssetCollections(
                        with: .smartAlbum,
                        subtype: .any,
                        options: nil
                    )
                    smartAlbums.enumerateObjects { collection, _, _ in
                        let album = Album.from(collection)
                        if !album.isEmpty {
                            albums.append(album)
                        }
                    }
                }
                
                // Fetch user albums
                if type == nil || type == .userAlbum {
                    let userAlbums = PHAssetCollection.fetchAssetCollections(
                        with: .album,
                        subtype: .any,
                        options: nil
                    )
                    userAlbums.enumerateObjects { collection, _, _ in
                        let album = Album.from(collection)
                        albums.append(album)
                    }
                }
                
                // Sort albums by title
                albums.sort { $0.localizedTitle < $1.localizedTitle }
                
                logInfo("Fetched \(albums.count) albums")
                continuation.resume(returning: albums)
            }
        }
    }
    
    func createAlbum(title: String) async throws -> Album {
        let status = checkAuthorizationStatus()
        guard status.isAuthorized else {
            throw MPhotosError.fromPHAuthorizationStatus(status)
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            var placeholder: PHObjectPlaceholder?
            
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: title)
                placeholder = request.placeholderForCreatedAssetCollection
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("创建相册失败: \(error.localizedDescription)"))
                    return
                }
                
                guard success, let placeholder = placeholder else {
                    continuation.resume(throwing: MPhotosError.custom("创建相册失败"))
                    return
                }
                
                let collections = PHAssetCollection.fetchAssetCollections(
                    withLocalIdentifiers: [placeholder.localIdentifier],
                    options: nil
                )
                
                if let collection = collections.firstObject {
                    let album = Album.from(collection)
                    logInfo("Created album: \(title)")
                    continuation.resume(returning: album)
                } else {
                    continuation.resume(throwing: MPhotosError.custom("无法获取创建的相册"))
                }
            }
        }
    }
    
    func deleteAlbum(_ album: Album) async throws {
        guard let collection = album.collection else {
            throw MPhotosError.custom("无法删除系统相册")
        }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                PHAssetCollectionChangeRequest.deleteAssetCollections([collection] as NSFastEnumeration)
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("删除相册失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Deleted album: \(album.title)")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("删除相册失败"))
                }
            }
        }
    }
    
    func renameAlbum(_ album: Album, to title: String) async throws {
        guard let collection = album.collection else {
            throw MPhotosError.custom("无法重命名系统相册")
        }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest(for: collection)
                request?.title = title
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("重命名相册失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Renamed album to: \(title)")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("重命名相册失败"))
                }
            }
        }
    }
    
    // MARK: - Assets
    func fetchAssets(in album: Album?) async throws -> PHFetchResult<PHAsset> {
        let status = checkAuthorizationStatus()
        guard status.isAuthorized else {
            throw MPhotosError.fromPHAuthorizationStatus(status)
        }
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let options = PHFetchOptions()
                options.sortDescriptors = Self.defaultSortDescriptors
                
                let result: PHFetchResult<PHAsset>
                
                if let album = album, let collection = album.collection {
                    result = PHAsset.fetchAssets(in: collection, options: options)
                } else {
                    result = PHAsset.fetchAssets(with: options)
                }
                
                logInfo("Fetched \(result.count) assets")
                continuation.resume(returning: result)
            }
        }
    }
    
    func fetchAllPhotos() async throws -> PHFetchResult<PHAsset> {
        return try await fetchAssets(in: nil)
    }
    
    func addAssets(_ assets: [PHAsset], to album: Album) async throws {
        guard let collection = album.collection else {
            throw MPhotosError.custom("无法添加到系统相册")
        }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest(for: collection)
                request?.addAssets(assets as NSFastEnumeration)
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("添加到相册失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Added \(assets.count) assets to album: \(album.title)")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("添加到相册失败"))
                }
            }
        }
    }
    
    func removeAssets(_ assets: [PHAsset], from album: Album) async throws {
        guard let collection = album.collection else {
            throw MPhotosError.custom("无法从系统相册移除")
        }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                let request = PHAssetCollectionChangeRequest(for: collection)
                request?.removeAssets(assets as NSFastEnumeration)
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("从相册移除失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Removed \(assets.count) assets from album: \(album.title)")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("从相册移除失败"))
                }
            }
        }
    }
    
    func deleteAssets(_ assets: [PHAsset]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                PHAssetChangeRequest.deleteAssets(assets as NSFastEnumeration)
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("删除资源失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Deleted \(assets.count) assets")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("删除资源失败"))
                }
            }
        }
    }
    
    func favoriteAssets(_ assets: [PHAsset], favorite: Bool) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                for asset in assets {
                    let request = PHAssetChangeRequest(for: asset)
                    request.isFavorite = favorite
                }
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("设置收藏失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Set favorite for \(assets.count) assets: \(favorite)")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("设置收藏失败"))
                }
            }
        }
    }
    
    // MARK: - Batch Operations
    func batchUpdate(_ assets: [PHAsset], operations: [AssetOperation]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            PHPhotoLibrary.shared().performChanges {
                for asset in assets {
                    let request = PHAssetChangeRequest(for: asset)
                    
                    for operation in operations {
                        switch operation {
                        case .favorite(let isFavorite):
                            request.isFavorite = isFavorite
                        case .hide(let isHidden):
                            request.isHidden = isHidden
                        case .delete:
                            PHAssetChangeRequest.deleteAssets([asset] as NSFastEnumeration)
                        case .addToAlbum(let album):
                            if let collection = album.collection {
                                let collectionRequest = PHAssetCollectionChangeRequest(for: collection)
                                collectionRequest?.addAssets([asset] as NSFastEnumeration)
                            }
                        case .removeFromAlbum(let album):
                            if let collection = album.collection {
                                let collectionRequest = PHAssetCollectionChangeRequest(for: collection)
                                collectionRequest?.removeAssets([asset] as NSFastEnumeration)
                            }
                        }
                    }
                }
            } completionHandler: { success, error in
                if let error = error {
                    continuation.resume(throwing: MPhotosError.custom("批量操作失败: \(error.localizedDescription)"))
                } else if success {
                    logInfo("Batch updated \(assets.count) assets with \(operations.count) operations")
                    continuation.resume()
                } else {
                    continuation.resume(throwing: MPhotosError.custom("批量操作失败"))
                }
            }
        }
    }
    
    // MARK: - Observers
    func addObserver(_ observer: any MediaObserver) {
        cleanupObservers()
        observers.append(WeakMediaObserver(observer))
        logDebug("Added media observer: \(type(of: observer))")
    }
    
    func removeObserver(_ observer: any MediaObserver) {
        observers.removeAll { $0.observer === observer }
        logDebug("Removed media observer: \(type(of: observer))")
    }
    
    private func cleanupObservers() {
        observers.removeAll { $0.observer == nil }
    }
    
    private func notifyObserversMediaChanged(_ changeInfo: PHChange) {
        cleanupObservers()
        observers.forEach { $0.observer?.mediaDidChange(changeInfo: changeInfo) }
    }
    
    private func notifyObserversAuthorizationChanged(_ status: PHAuthorizationStatus) {
        cleanupObservers()
        observers.forEach { $0.observer?.authorizationStatusDidChange(status: status) }
    }
}

// MARK: - PHPhotoLibraryChangeObserver
extension MediaManager: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInfo: PHChange) {
        DispatchQueue.main.async { [weak self] in
            self?.photoLibraryChangeSubject.send(changeInfo)
            self?.notifyObserversMediaChanged(changeInfo)
            logDebug("Photo library changed")
        }
    }
}

// MARK: - WeakMediaObserver Helper
private class WeakMediaObserver {
    weak var observer: (any MediaObserver)?
    
    init(_ observer: any MediaObserver) {
        self.observer = observer
    }
}