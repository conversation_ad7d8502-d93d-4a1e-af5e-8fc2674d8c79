//
//  MediaManaging.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Photos
import Combine

// MARK: - Media Management Protocol
protocol MediaManaging {
    // MARK: - Authorization
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func checkAuthorizationStatus() -> PHAuthorizationStatus
    
    // MARK: - Albums
    func fetchAlbums(ofType type: AlbumType?) async throws -> [Album]
    func createAlbum(title: String) async throws -> Album
    func deleteAlbum(_ album: Album) async throws
    func renameAlbum(_ album: Album, to title: String) async throws
    
    // MARK: - Assets
    func fetchAssets(in album: Album?) async throws -> PHFetchResult<PHAsset>
    func fetchAllPhotos() async throws -> PHFetchResult<PHAsset>
    func addAssets(_ assets: [PHAsset], to album: Album) async throws
    func removeAssets(_ assets: [PHAsse<PERSON>], from album: Album) async throws
    func deleteAssets(_ assets: [PHAsset]) async throws
    func favoriteAssets(_ assets: [PHAsset], favorite: Bool) async throws
    
    // MARK: - Batch Operations
    func batchUpdate(_ assets: [PHAsset], operations: [AssetOperation]) async throws
    
    // MARK: - Observers
    func addObserver(_ observer: any MediaObserver)
    func removeObserver(_ observer: any MediaObserver)
    
    // MARK: - Publishers
    var authorizationStatusPublisher: AnyPublisher<PHAuthorizationStatus, Never> { get }
    var photoLibraryChangePublisher: AnyPublisher<PHChange, Never> { get }
}

// MARK: - Media Observer Protocol
protocol MediaObserver: AnyObject {
    func mediaDidChange(changeInfo: PHChange)
    func authorizationStatusDidChange(status: PHAuthorizationStatus)
}

// MARK: - Asset Operations
enum AssetOperation {
    case favorite(Bool)
    case hide(Bool)
    case delete
    case addToAlbum(Album)
    case removeFromAlbum(Album)
}

// MARK: - Asset Filters
struct AssetFilter {
    let mediaTypes: [PHAssetMediaType]?
    let startDate: Date?
    let endDate: Date?
    let includeHidden: Bool
    let includeFavorites: Bool?
    
    init(
        mediaTypes: [PHAssetMediaType]? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        includeHidden: Bool = false,
        includeFavorites: Bool? = nil
    ) {
        self.mediaTypes = mediaTypes
        self.startDate = startDate
        self.endDate = endDate
        self.includeHidden = includeHidden
        self.includeFavorites = includeFavorites
    }
    
    func apply(to options: PHFetchOptions) {
        var predicates: [NSPredicate] = []
        
        if let mediaTypes = mediaTypes {
            let mediaTypePredicate = NSPredicate(format: "mediaType IN %@", mediaTypes.map { $0.rawValue })
            predicates.append(mediaTypePredicate)
        }
        
        if let startDate = startDate {
            let startPredicate = NSPredicate(format: "creationDate >= %@", startDate as NSDate)
            predicates.append(startPredicate)
        }
        
        if let endDate = endDate {
            let endPredicate = NSPredicate(format: "creationDate <= %@", endDate as NSDate)
            predicates.append(endPredicate)
        }
        
        if !includeHidden {
            let hiddenPredicate = NSPredicate(format: "isHidden == NO")
            predicates.append(hiddenPredicate)
        }
        
        if let includeFavorites = includeFavorites {
            let favoritesPredicate = NSPredicate(format: "isFavorite == %@", NSNumber(value: includeFavorites))
            predicates.append(favoritesPredicate)
        }
        
        if !predicates.isEmpty {
            options.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        }
    }
}

// MARK: - Sort Descriptors
extension MediaManaging {
    static var defaultSortDescriptors: [NSSortDescriptor] {
        return [NSSortDescriptor(key: "creationDate", ascending: false)]
    }
    
    static var titleSortDescriptors: [NSSortDescriptor] {
        return [NSSortDescriptor(key: "localizedTitle", ascending: true)]
    }
}