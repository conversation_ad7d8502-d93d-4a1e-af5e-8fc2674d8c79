//
//  AppDelegate.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/2.
//

import UIKit
import CoreData

@main
class AppDelegate: UIResponder, UIApplicationDelegate {



    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        setupServices()
        logInfo("Application did finish launching")
        return true
    }
    
    private func setupServices() {
        // Register all default services (includes MediaManaging and CacheManaging)
        Container.registerDefaultServices()
        logInfo("Services registered successfully")
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    // MARK: - Core Data Support
    func applicationWillTerminate(_ application: UIApplication) {
        // Save Core Data context when app terminates
        CoreDataStack.shared.saveContext()
    }

}

