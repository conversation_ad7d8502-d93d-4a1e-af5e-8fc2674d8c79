//
//  BaseViewModel.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Combine

@MainActor
class BaseViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var error: Error?
    @Published var isInitialized = false
    
    private(set) var cancellables = Set<AnyCancellable>()
    
    init() {
        logDebug("Initializing \(String(describing: type(of: self)))")
    }
    
    deinit {
        logDebug("Deinitializing \(String(describing: type(of: self)))")
        cancellables.removeAll()
    }
    
    // MARK: - Loading State Management
    func startLoading() {
        isLoading = true
        error = nil
    }
    
    func stopLoading() {
        isLoading = false
    }
    
    func setLoading(_ loading: Bool) {
        isLoading = loading
    }
    
    // MARK: - Error Handling
    func handleError(_ error: Error) {
        logError(error)
        self.error = error
        isLoading = false
    }
    
    func clearError() {
        error = nil
    }
    
    // MARK: - Lifecycle Methods
    func initialize() async {
        guard !isInitialized else { return }
        
        startLoading()
        
        do {
            try await performInitialization()
            isInitialized = true
            logInfo("\(String(describing: type(of: self))) initialized successfully")
        } catch {
            handleError(error)
        }
        
        stopLoading()
    }
    
    // Override this method in subclasses
    func performInitialization() async throws {
        // Default implementation does nothing
    }
    
    // MARK: - Async Operation Helpers
    func executeAsync<T>(_ operation: @escaping () async throws -> T) async -> T? {
        startLoading()
        
        do {
            let result = try await operation()
            stopLoading()
            return result
        } catch {
            handleError(error)
            return nil
        }
    }
    
    func executeAsync(_ operation: @escaping () async throws -> Void) async {
        startLoading()
        
        do {
            try await operation()
            stopLoading()
        } catch {
            handleError(error)
        }
    }
    
    // MARK: - Publisher Helpers
    func bindPublisher<T>(
        _ publisher: AnyPublisher<T, Error>,
        to keyPath: ReferenceWritableKeyPath<BaseViewModel, T>
    ) {
        publisher
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                    self?.stopLoading()
                },
                receiveValue: { [weak self] value in
                    self?[keyPath: keyPath] = value
                }
            )
            .store(in: &cancellables)
    }
    
    func bindLoadingPublisher<T>(
        _ publisher: AnyPublisher<T, Error>,
        to action: @escaping (T) -> Void
    ) {
        startLoading()
        
        publisher
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.stopLoading()
                    if case .failure(let error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: action
            )
            .store(in: &cancellables)
    }
}

// MARK: - Error Helpers
extension BaseViewModel {
    func showError(_ message: String) {
        let error = MPhotosError.custom(message)
        handleError(error)
    }
    
    func showNetworkError() {
        let error = MPhotosError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: Constants.ErrorMessages.networkUnavailable]))
        handleError(error)
    }
}