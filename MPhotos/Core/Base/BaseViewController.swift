//
//  BaseViewController.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import UIKit
import Combine

class BaseViewController: UIViewController {
    
    // MARK: - Properties
    internal var cancellables = Set<AnyCancellable>()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        logDebug("ViewDidLoad: \(String(describing: type(of: self)))")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        logDebug("ViewWillAppear: \(String(describing: type(of: self)))")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logDebug("ViewDidAppear: \(String(describing: type(of: self)))")
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        logDebug("ViewWillDisappear: \(String(describing: type(of: self)))")
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        logDebug("ViewDidDisappear: \(String(describing: type(of: self)))")
    }
    
    deinit {
        logDebug("Deinitializing \(String(describing: type(of: self)))")
        cancellables.removeAll()
    }
    
    // MARK: - Setup Methods (Override in subclasses)
    func setupUI() {
        view.backgroundColor = .appBackground
    }
    
    func setupBindings() {
        // Override in subclasses to setup data bindings
    }
    
    // MARK: - Memory Warning
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        logWarning("Memory warning received in \(String(describing: type(of: self)))")
        handleMemoryWarning()
    }
    
    func handleMemoryWarning() {
        // Override in subclasses to handle memory warnings
    }
    
    // MARK: - Loading State
    private var loadingView: UIView?
    
    func showLoading() {
        guard loadingView == nil else { return }
        
        let loading = UIView()
        loading.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        loading.translatesAutoresizingMaskIntoConstraints = false
        
        let activityIndicator = UIActivityIndicatorView(style: .large)
        activityIndicator.color = .white
        activityIndicator.startAnimating()
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        
        loading.addSubview(activityIndicator)
        view.addSubview(loading)
        
        NSLayoutConstraint.activate([
            loading.topAnchor.constraint(equalTo: view.topAnchor),
            loading.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            loading.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            loading.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            activityIndicator.centerXAnchor.constraint(equalTo: loading.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: loading.centerYAnchor)
        ])
        
        loadingView = loading
    }
    
    func hideLoading() {
        loadingView?.removeFromSuperview()
        loadingView = nil
    }
    
    // MARK: - Error Handling
    func displayError(_ error: Error) {
        hideLoading()
        
        let message = (error as? MPhotosError)?.errorDescription ?? error.localizedDescription
        showAlert(title: "错误", message: message)
        logError(error)
    }
    
    // MARK: - Navigation Helpers
    func pushViewController(_ viewController: UIViewController, animated: Bool = true) {
        navigationController?.pushViewController(viewController, animated: animated)
    }
    
    func popViewController(animated: Bool = true) {
        navigationController?.popViewController(animated: animated)
    }
    
    func presentModally(_ viewController: UIViewController, animated: Bool = true) {
        present(viewController, animated: animated)
    }
    
    func dismissModal(animated: Bool = true, completion: (() -> Void)? = nil) {
        dismiss(animated: animated, completion: completion)
    }
}

// MARK: - ViewModel Binding
extension BaseViewController {
    func bindViewModel<T: BaseViewModel>(_ viewModel: T) {
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading {
                    self?.showLoading()
                } else {
                    self?.hideLoading()
                }
            }
            .store(in: &cancellables)
        
        viewModel.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.displayError(error)
            }
            .store(in: &cancellables)
    }
}