//
//  ServiceContainer.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation

protocol ServiceContaining {
    func register<T>(_ serviceType: T.Type, factory: @escaping () -> T)
    func register<T>(_ serviceType: T.Type, instance: T)
    func resolve<T>(_ serviceType: T.Type) -> T
    func resolve<T>() -> T
}

final class ServiceContainer: ServiceContaining {
    static let shared = ServiceContainer()
    
    private var services: [String: Any] = [:]
    private let queue = DispatchQueue(label: "serviceContainer", attributes: .concurrent)
    
    private init() {}
    
    func register<T>(_ serviceType: T.Type, factory: @escaping () -> T) {
        let key = String(describing: serviceType)
        queue.sync(flags: .barrier) {
            self.services[key] = factory
        }
    }

    func register<T>(_ serviceType: T.Type, instance: T) {
        let key = String(describing: serviceType)
        queue.sync(flags: .barrier) {
            self.services[key] = instance
        }
    }
    
    func resolve<T>(_ serviceType: T.Type) -> T {
        let key = String(describing: serviceType)
        
        return queue.sync {
            if let instance = services[key] as? T {
                return instance
            }
            
            if let factory = services[key] as? () -> T {
                let instance = factory()
                services[key] = instance
                return instance
            }
            
            fatalError("Service of type \(serviceType) not registered")
        }
    }
    
    func resolve<T>() -> T {
        return resolve(T.self)
    }

    func isRegistered<T>(_ serviceType: T.Type) -> Bool {
        let key = String(describing: serviceType)
        return queue.sync {
            return services[key] != nil
        }
    }

    func removeAll() {
        queue.async(flags: .barrier) {
            self.services.removeAll()
        }
    }
    
    func remove<T>(_ serviceType: T.Type) {
        let key = String(describing: serviceType)
        queue.async(flags: .barrier) {
            self.services.removeValue(forKey: key)
        }
    }
}

// MARK: - Service Registration Helper
extension ServiceContainer {
    func registerDefaultServices() {
        logInfo("Registering default services")
        
        // Register Core Data stack
        register(CoreDataStack.self) {
            CoreDataStack.shared
        }
        
        // Register repositories
        register(MetadataRepositoryProtocol.self) {
            MetadataRepository()
        }
        
        // Register MediaManager
        register(MediaManaging.self) {
            MediaManager.shared
        }
        
        // Register CacheManager
        register(CacheManaging.self) {
            CacheManager.shared
        }
        
        logInfo("Default services registered")
    }
}

// MARK: - Global Access
var Container: ServiceContainer {
    return ServiceContainer.shared
}