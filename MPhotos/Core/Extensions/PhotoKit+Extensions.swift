//
//  PhotoKit+Extensions.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Photos
import UIKit

// MARK: - PHAsset Extensions
extension PHAsset {
    var isInCloud: Bool {
        guard let resource = PHAssetResource.assetResources(for: self).first else { return false }
        return !(resource.value(forKey: "locallyAvailable") as? Bool ?? true)
    }
    
    var fileSize: Int64 {
        let resources = PHAssetResource.assetResources(for: self)
        return resources.first?.value(forKey: "fileSize") as? Int64 ?? 0
    }
    
    var isVideo: Bool {
        return mediaType == .video
    }
    
    var isImage: Bool {
        return mediaType == .image
    }
    
    var durationString: String? {
        guard mediaType == .video else { return nil }
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    func thumbnail(size: CGSize, completion: @escaping (UIImage?) -> Void) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        
        PHImageManager.default().requestImage(
            for: self,
            targetSize: size,
            contentMode: .aspectFill,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                completion(image)
            }
        }
    }
    
    @discardableResult
    func requestFullResolutionImage(completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.progressHandler = { progress, _, _, _ in
            print("Image download progress: \(progress)")
        }
        
        return PHImageManager.default().requestImage(
            for: self,
            targetSize: PHImageManagerMaximumSize,
            contentMode: .aspectFit,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                completion(image)
            }
        }
    }
}

// MARK: - PHAssetCollection Extensions
extension PHAssetCollection {
    var keyAsset: PHAsset? {
        let assets = PHAsset.fetchAssets(in: self, options: nil)
        return assets.firstObject
    }
    
    var assetsCount: Int {
        return PHAsset.fetchAssets(in: self, options: nil).count
    }
    
    func fetchAssets(sortBy descriptors: [NSSortDescriptor]? = nil) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = descriptors ?? [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: self, options: options)
    }
}

// MARK: - PHAuthorizationStatus Extensions
extension PHAuthorizationStatus {
    var isAuthorized: Bool {
        switch self {
        case .authorized, .limited:
            return true
        default:
            return false
        }
    }
    
    var localizedDescription: String {
        switch self {
        case .notDetermined:
            return "未确定"
        case .restricted:
            return "受限制"
        case .denied:
            return "已拒绝"
        case .authorized:
            return "已授权"
        case .limited:
            return "部分授权"
        @unknown default:
            return "未知状态"
        }
    }
}

// MARK: - PHFetchResult Extensions
extension PHFetchResult where ObjectType == PHAsset {
    func toArray() -> [PHAsset] {
        var assets: [PHAsset] = []
        enumerateObjects { asset, _, _ in
            assets.append(asset)
        }
        return assets
    }
    
    func assets(in range: NSRange) -> [PHAsset] {
        guard range.location < count,
              range.location + range.length <= count else {
            return []
        }
        
        var assets: [PHAsset] = []
        for i in range.location..<(range.location + range.length) {
            assets.append(object(at: i))
        }
        return assets
    }
}