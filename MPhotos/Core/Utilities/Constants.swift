//
//  Constants.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import UIKit

struct Constants {
    
    // MARK: - App Info
    struct App {
        static let name = "MPhotos"
        static let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        static let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    // MARK: - Cache Configuration
    struct Cache {
        static let memoryCacheLimit = 50 * 1024 * 1024 // 50MB
        static let memoryCacheCountLimit = 200 // 200 images
        static let thumbnailCacheExpiry: TimeInterval = 60 * 60 * 24 // 24 hours
    }
    
    // MARK: - UI Configuration
    struct UI {
        static let thumbnailSize = CGSize(width: 200, height: 200)
        static let previewSize = CGSize(width: 375, height: 375)
        static let animationDuration: TimeInterval = 0.3
        static let cornerRadius: CGFloat = 8.0
        
        struct Grid {
            static let minimumItemSpacing: CGFloat = 1.0
            static let minimumLineSpacing: CGFloat = 1.0
            static let itemsPerRow = 3
        }
        
        struct CollectionView {
            static let preloadRange = 20
            static let prefetchThreshold = 10
        }
    }
    
    // MARK: - Performance Configuration
    struct Performance {
        static let maxConcurrentImageRequests = 10
        static let lowMemoryThreshold = 100 * 1024 * 1024 // 100MB
        static let criticalMemoryThreshold = 200 * 1024 * 1024 // 200MB
        static let targetFrameRate: Double = 60.0
    }
    
    // MARK: - User Defaults Keys
    struct UserDefaultsKeys {
        static let hasRequestedPhotoPermission = "hasRequestedPhotoPermission"
        static let preferredGridSize = "preferredGridSize"
        static let lastSelectedAlbum = "lastSelectedAlbum"
        static let cacheSizeLimit = "cacheSizeLimit"
    }
    
    // MARK: - Notification Names
    struct Notifications {
        static let photoLibraryDidChange = Notification.Name("photoLibraryDidChange")
        static let memoryWarningReceived = Notification.Name("memoryWarningReceived")
        static let cacheDidClear = Notification.Name("cacheDidClear")
    }
    
    // MARK: - Error Messages
    struct ErrorMessages {
        static let photoAccessDenied = "需要访问相册权限才能浏览照片"
        static let photoAccessRestricted = "相册访问受到限制"
        static let networkUnavailable = "网络连接不可用"
        static let assetNotFound = "找不到指定的照片或视频"
        static let exportFailed = "导出失败"
        static let editFailed = "编辑失败"
        static let unknownError = "发生未知错误"
    }
    
    // MARK: - System Configuration
    struct System {
        static let backgroundTaskIdentifier = "com.mphotos.background.processing"
        static let maxBackgroundTaskDuration: TimeInterval = 30.0
    }
}