//
//  MPhotosError.swift
//  MPhotos
//
//  Created by <PERSON> on 2025/8/1.
//

import Foundation
import Photos

enum MPhotosError: LocalizedError {
    case unauthorized
    case restricted
    case assetNotFound
    case networkError(Error)
    case cacheError(String)
    case exportFailed(String)
    case editFailed(String)
    case custom(String)
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return Constants.ErrorMessages.photoAccessDenied
        case .restricted:
            return Constants.ErrorMessages.photoAccessRestricted
        case .assetNotFound:
            return Constants.ErrorMessages.assetNotFound
        case .networkError(let error):
            return "\(Constants.ErrorMessages.networkUnavailable): \(error.localizedDescription)"
        case .cacheError(let message):
            return "缓存错误: \(message)"
        case .exportFailed(let message):
            return "\(Constants.ErrorMessages.exportFailed): \(message)"
        case .editFailed(let message):
            return "\(Constants.ErrorMessages.editFailed): \(message)"
        case .custom(let message):
            return message
        case .unknown:
            return Constants.ErrorMessages.unknownError
        }
    }
    
    var failureReason: String? {
        switch self {
        case .unauthorized:
            return "应用需要访问相册权限"
        case .restricted:
            return "设备限制了相册访问"
        case .assetNotFound:
            return "资源可能已被删除或移动"
        case .networkError:
            return "网络连接问题"
        case .cacheError:
            return "缓存系统错误"
        case .exportFailed:
            return "导出过程中发生错误"
        case .editFailed:
            return "编辑过程中发生错误"
        case .custom:
            return nil
        case .unknown:
            return "未知的应用错误"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .unauthorized:
            return "请在设置中允许访问所有照片"
        case .restricted:
            return "请检查设备的访问限制设置"
        case .assetNotFound:
            return "请刷新相册或重新选择资源"
        case .networkError:
            return "请检查网络连接后重试"
        case .cacheError:
            return "请清理缓存后重试"
        case .exportFailed:
            return "请检查存储空间后重试"
        case .editFailed:
            return "请重试编辑操作"
        case .custom:
            return nil
        case .unknown:
            return "请重启应用后重试"
        }
    }
}

// MARK: - Error Creation Helpers
extension MPhotosError {
    static func fromPHAuthorizationStatus(_ status: PHAuthorizationStatus) -> MPhotosError {
        switch status {
        case .denied:
            return .unauthorized
        case .restricted:
            return .restricted
        case .notDetermined, .limited, .authorized:
            return .unknown
        @unknown default:
            return .unknown
        }
    }
    
    static func networkError(from error: Error) -> MPhotosError {
        return .networkError(error)
    }
    
    static func cache(_ message: String) -> MPhotosError {
        return .cacheError(message)
    }
}