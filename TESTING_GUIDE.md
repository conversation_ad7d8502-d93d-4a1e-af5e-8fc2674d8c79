# MPhotos 测试指南

## 🎯 测试策略概览
- **测试金字塔**: 单元测试(70%) > 集成测试(20%) > UI测试(10%)
- **覆盖率目标**: 核心业务逻辑 >80%, 整体 >70%
- **TDD 实践**: 关键功能优先编写测试
- **Mock 策略**: 协议驱动的依赖注入

## 🧪 单元测试

### ViewModel 测试模板
```swift
import XCTest
import Combine
@testable import MPhotos

class FeatureViewModelTests: XCTestCase {
    var sut: FeatureViewModel!
    var mockService: MockService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockService = MockService()
        sut = FeatureViewModel(service: mockService)
        cancellables = []
    }
    
    override func tearDown() {
        sut = nil
        mockService = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Success Cases
    func testLoadData_Success_UpdatesItems() {
        // Given
        let expectedItems = [createMockData()]
        mockService.dataToReturn = expectedItems
        let expectation = XCTestExpectation(description: "Data loaded")
        
        // When
        sut.$items
            .dropFirst()
            .sink { items in
                XCTAssertEqual(items.count, expectedItems.count)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        sut.loadData()
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNil(sut.error)
    }
    
    // MARK: - Error Cases
    func testLoadData_Failure_SetsError() {
        // Given
        mockService.shouldFail = true
        mockService.errorToThrow = TestError.networkError
        
        // When
        sut.loadData()
        
        // Then
        XCTAssertNotNil(sut.error)
        XCTAssertTrue(sut.items.isEmpty)
    }
}
```

### Service 测试模板
```swift
class MediaManagerTests: XCTestCase {
    var sut: MediaManager!
    var mockPhotoLibrary: MockPHPhotoLibrary!
    
    override func setUp() {
        super.setUp()
        mockPhotoLibrary = MockPHPhotoLibrary()
        sut = MediaManager(photoLibrary: mockPhotoLibrary)
    }
    
    func testFetchAlbums_Success_ReturnsAlbums() async throws {
        // Given
        let expectedAlbums = [createMockAlbum()]
        mockPhotoLibrary.albumsToReturn = expectedAlbums
        
        // When
        let albums = try await sut.fetchAlbums(ofType: .user)
        
        // Then
        XCTAssertEqual(albums.count, expectedAlbums.count)
        XCTAssertTrue(mockPhotoLibrary.fetchAlbumsCalled)
    }
}
```

### Cache 测试模板
```swift
class CacheManagerTests: XCTestCase {
    var sut: CacheManager!
    
    override func setUp() {
        super.setUp()
        sut = CacheManager()
    }
    
    override func tearDown() {
        Task { await sut.clearAll() }
        super.tearDown()
    }
    
    func testStoreAndRetrieve_Success() async {
        // Given
        let testImage = UIImage(systemName: "photo")!
        let key = "test_key"
        
        // When
        await sut.store(testImage, for: key)
        let retrievedImage = await sut.image(for: key)
        
        // Then
        XCTAssertNotNil(retrievedImage)
        XCTAssertEqual(testImage.size, retrievedImage?.size)
    }
}
```

## 🔗 集成测试

### Repository 集成测试
```swift
class MediaRepositoryIntegrationTests: XCTestCase {
    var sut: MediaRepository!
    
    override func setUp() {
        super.setUp()
        sut = MediaRepository(
            mediaManager: MediaManager.shared,
            cacheManager: CacheManager.shared,
            coreDataStack: CoreDataStack.test
        )
    }
    
    func testFetchAndCacheAssets_EndToEnd() async throws {
        // Given
        let album = try await createTestAlbum()
        
        // When
        let assets = try await sut.fetchAssets(in: album)
        
        // Then
        XCTAssertFalse(assets.isEmpty)
        
        // Verify caching
        let cachedAssets = await sut.getCachedAssets(for: album.identifier)
        XCTAssertEqual(assets.count, cachedAssets?.count)
    }
}
```

## 🎨 UI 测试

### Navigation 测试
```swift
class NavigationUITests: XCTestCase {
    var app: XCUIApplication!
    
    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launch()
    }
    
    func testPhotoGridToDetailNavigation() {
        // Given
        let photoGrid = app.collectionViews["PhotoGrid"]
        XCTAssertTrue(photoGrid.exists)
        
        // When
        photoGrid.cells.firstMatch.tap()
        
        // Then
        let detailView = app.otherElements["PhotoDetailView"]
        XCTAssertTrue(detailView.waitForExistence(timeout: 2))
        
        // Navigate back
        app.navigationBars.buttons.firstMatch.tap()
        XCTAssertTrue(photoGrid.waitForExistence(timeout: 2))
    }
}
```

## 🎭 Mock 对象

### Mock Service 基类
```swift
class MockServiceBase {
    var shouldFail = false
    var errorToThrow: Error = TestError.generic
    var callCount = 0
}

class MockMediaManager: MockServiceBase, MediaManaging {
    var albumsToReturn: [Album] = []
    var assetsToReturn: [PHAsset] = []
    var authorizationStatus: PHAuthorizationStatus = .authorized
    
    var fetchAlbumsCalled = false
    var fetchAssetsCalled = false
    var requestAuthorizationCalled = false
    
    func requestAuthorization() async throws -> PHAuthorizationStatus {
        requestAuthorizationCalled = true
        callCount += 1
        
        if shouldFail {
            throw errorToThrow
        }
        return authorizationStatus
    }
    
    func fetchAlbums(ofType type: AlbumType) -> AnyPublisher<[Album], Error> {
        fetchAlbumsCalled = true
        callCount += 1
        
        if shouldFail {
            return Fail(error: errorToThrow)
                .eraseToAnyPublisher()
        }
        
        return Just(albumsToReturn)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset> {
        fetchAssetsCalled = true
        callCount += 1
        
        // Return mock fetch result
        return MockPHFetchResult(assets: assetsToReturn)
    }
}
```

### Mock Cache Manager
```swift
class MockCacheManager: CacheManaging {
    private var storage: [String: UIImage] = [:]
    
    var storeImageCalled = false
    var loadImageCalled = false
    
    func store(_ image: UIImage, for key: String) async {
        storeImageCalled = true
        storage[key] = image
    }
    
    func image(for key: String) async -> UIImage? {
        loadImageCalled = true
        return storage[key]
    }
    
    func removeImage(for key: String) async {
        storage.removeValue(forKey: key)
    }
    
    func clearAll() async {
        storage.removeAll()
    }
}
```

## ⚡ 性能测试

### 内存泄露测试
```swift
class MemoryLeakTests: XCTestCase {
    func testViewModelDoesNotLeak() {
        weak var weakViewModel: FeatureViewModel?
        
        autoreleasepool {
            let mockService = MockService()
            let viewModel = FeatureViewModel(service: mockService)
            weakViewModel = viewModel
            
            // 使用 viewModel
            viewModel.loadData()
        }
        
        XCTAssertNil(weakViewModel, "ViewModel should be deallocated")
    }
}
```

### 性能基准测试
```swift
class PerformanceTests: XCTestCase {
    func testLargeDatasetScrolling() {
        let mediaManager = MediaManager.shared
        let assets = createLargeAssetArray(count: 1000)
        
        measure {
            for asset in assets {
                _ = mediaManager.requestThumbnail(for: asset, size: CGSize(width: 200, height: 200))
            }
        }
    }
    
    func testCachePerformance() {
        let cacheManager = CacheManager()
        let testImages = createTestImages(count: 100)
        
        measure {
            Task {
                for (index, image) in testImages.enumerated() {
                    await cacheManager.store(image, for: "key_\(index)")
                }
                
                for index in 0..<100 {
                    _ = await cacheManager.image(for: "key_\(index)")
                }
            }
        }
    }
}
```

## 🛠️ 测试工具类

### Test Builders
```swift
class TestDataBuilder {
    static func createMockAlbum(
        identifier: String = "test_album",
        title: String = "Test Album",
        type: AlbumType = .user,
        assetCount: Int = 10
    ) -> Album {
        return Album(
            identifier: identifier,
            title: title,
            type: type,
            assetCount: assetCount,
            collection: nil
        )
    }
    
    static func createMockAsset(
        identifier: String = "test_asset",
        mediaType: PHAssetMediaType = .image,
        pixelWidth: Int = 1920,
        pixelHeight: Int = 1080
    ) -> PHAsset {
        // 使用 PHAsset 的测试工厂方法
        return PHAsset.createTestAsset(
            identifier: identifier,
            mediaType: mediaType,
            pixelWidth: pixelWidth,
            pixelHeight: pixelHeight
        )
    }
}
```

### Test Helpers
```swift
extension XCTestCase {
    func waitForPublisher<T: Publisher>(
        _ publisher: T,
        timeout: TimeInterval = 1.0,
        file: StaticString = #file,
        line: UInt = #line
    ) throws -> T.Output where T.Failure == Never {
        var result: T.Output?
        let expectation = XCTestExpectation(description: "Publisher")
        
        let cancellable = publisher
            .sink { value in
                result = value
                expectation.fulfill()
            }
        
        wait(for: [expectation], timeout: timeout)
        cancellable.cancel()
        
        guard let unwrappedResult = result else {
            XCTFail("Publisher did not emit value", file: file, line: line)
            throw TestError.timeout
        }
        
        return unwrappedResult
    }
}
```

## ✅ 测试检查清单

### 单元测试检查
- [ ] 所有公开方法有对应测试
- [ ] 成功和失败场景都覆盖
- [ ] Mock 对象使用正确
- [ ] 测试独立，无顺序依赖
- [ ] 测试命名清晰描述场景

### 集成测试检查
- [ ] 关键用户流程覆盖
- [ ] 跨层依赖测试
- [ ] 数据持久化测试
- [ ] 网络和缓存集成测试

### 性能测试检查
- [ ] 内存泄露检测
- [ ] 关键路径性能基准
- [ ] 大数据集处理测试
- [ ] UI 响应性测试

## 📊 测试报告

### 覆盖率监控
```bash
# 生成覆盖率报告
xcodebuild test -scheme MPhotos \
  -destination 'platform=iOS Simulator,name=iPhone 15 Pro' \
  -enableCodeCoverage YES

# 查看覆盖率
xcrun llvm-cov show MPhotos.app/MPhotos \
  -instr-profile=Coverage.profdata
```

### CI/CD 集成
```yaml
# GitHub Actions 示例
- name: Run Tests
  run: |
    xcodebuild test \
      -scheme MPhotos \
      -destination 'platform=iOS Simulator,name=iPhone 15 Pro' \
      -enableCodeCoverage YES \
      -resultBundlePath TestResults.xcresult

- name: Check Coverage
  run: |
    xcrun llvm-cov report MPhotos.app/MPhotos \
      -instr-profile=Coverage.profdata \
      --format=text > coverage.txt
```

---

**测试原则**: 快速、可靠、独立、可重复、自验证