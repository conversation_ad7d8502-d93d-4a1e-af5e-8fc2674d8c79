# MPhotos - Claude Code 开发指引

## 项目概览
高性能 iOS Photos 克隆应用，采用 MVVM-C 架构模式，基于 PhotoKit + Core Data + 二级缓存系统。

## 🧭 阶段导航：我在哪个阶段？

### 当前状态检查
```bash
# 检查当前开发阶段
grep -A 5 "🔄 进行中\|📋 等待开始" ../DEVELOPMENT_PROGRESS.md
```

### 📅 按阶段使用本文档

#### Phase 1: 基础架构 (我需要什么?)
🎯 **核心任务**: DI容器、MediaManager、CacheManager、Core Data
📖 **重点章节**: 
- [核心架构组件](#核心架构组件) - 了解要实现的接口
- [代码生成模板](#代码生成模板) - 快速创建基础类
- [性能优化要点](#性能优化要点) - 避免早期性能问题

#### Phase 2: UI开发 (我需要什么?)
🎯 **核心任务**: CollectionView、Cell、ViewController、转场动画
📖 **重点章节**:
- [代码生成模板](#代码生成模板) - ViewModel和Cell模板
- [性能优化要点](#性能优化要点) - UI响应性和内存管理
- [调试技巧](#调试技巧) - 性能监控工具

#### Phase 3: 功能完善 (我需要什么?)
🎯 **核心任务**: 编辑功能、相册管理、批量操作
📖 **重点章节**:
- [错误处理](#错误处理) - 统一错误管理
- [性能优化要点](#性能优化要点) - 内存管理策略
- [AI 开发提示](#ai-开发提示) - 复杂功能实现

#### Phase 4: 优化发布 (我需要什么?)
🎯 **核心任务**: 性能优化、测试完善、发布准备
📖 **重点章节**:
- [调试技巧](#调试技巧) - 性能分析工具
- [开发规范参考](#开发规范参考) - 代码质量检查

## 快速开始

### 开发环境
- Xcode 15.0+
- iOS 16.4+
- Swift 5.9+

### 常用命令
```bash
# 清理构建
rm -rf ~/Library/Developer/Xcode/DerivedData/MPhotos-*

# 运行测试
xcodebuild test -scheme MPhotos -destination 'platform=iOS Simulator,name=iPhone 15 Pro'

# 性能分析
xcrun xctrace record --template "Time Profiler" --launch -- /path/to/MPhotos.app
```

## 核心架构组件

### 1. MediaManager (MPhotos/Domain/Managers/)
```swift
// 主要职责：PhotoKit 封装、权限管理、资源操作
protocol MediaManaging {
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func fetchAlbums(ofType type: AlbumType) -> [Album]
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset>
}
```

### 2. CacheManager (MPhotos/Data/Cache/)
```swift
// 二级缓存：NSCache(内存) + PHCachingImageManager(系统)
func requestImage(for asset: PHAsset, targetSize: CGSize) async -> UIImage?
```

### 3. ViewModel 基类 (MPhotos/Presentation/Base/)
```swift
// MVVM 模式基础，包含通用状态管理
class BaseViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var error: Error?
}
```

## 代码生成模板

### ViewModel 模板
```swift
class {{FeatureName}}ViewModel: BaseViewModel {
    @Published var {{dataProperty}}: [{{DataType}}] = []
    
    private let {{serviceName}}: {{ServiceType}}
    private var cancellables = Set<AnyCancellable>()
    
    init({{serviceName}}: {{ServiceType}}) {
        self.{{serviceName}} = {{serviceName}}
        super.init()
    }
    
    func fetch{{DataType}}s() {
        isLoading = true
        error = nil
        
        {{serviceName}}.fetch{{DataType}}s()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] items in
                    self?.{{dataProperty}} = items
                }
            )
            .store(in: &cancellables)
    }
}
```

### Cell 模板
```swift
class {{FeatureName}}Cell: UICollectionViewCell {
    static let identifier = "{{FeatureName}}Cell"
    
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    
    private var requestID: PHImageRequestID?
    
    func configure(with asset: PHAsset) {
        // 取消之前的请求
        if let requestID = requestID {
            PHImageManager.default().cancelImageRequest(requestID)
        }
        
        // 请求缩略图
        let size = CGSize(width: 200, height: 200)
        requestID = CacheManager.shared.requestImage(
            for: asset,
            targetSize: size
        ) { [weak self] image in
            DispatchQueue.main.async {
                self?.imageView.image = image
            }
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        if let requestID = requestID {
            PHImageManager.default().cancelImageRequest(requestID)
        }
        imageView.image = nil
    }
}
```

## 性能优化要点

### 内存管理
- 使用 `weak self` 避免循环引用
- 及时取消 PHImageRequest
- 在 `prepareForReuse` 中清理资源

### 缓存策略
- 内存缓存：NSCache，容量 50MB，数量 200 张
- 系统缓存：PHCachingImageManager 自动管理
- 预加载：可见区域前后各 20 个项目

### UI 响应性
- 主线程更新 UI
- 滚动时降低图片质量
- 使用 `deliveryMode = .opportunistic` 快速显示

## 错误处理

### 统一错误类型
```swift
enum MPhotosError: LocalizedError {
    case unauthorized
    case assetNotFound
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .unauthorized: return "需要相册访问权限"
        case .assetNotFound: return "找不到指定照片"
        case .networkError(let error): return "网络错误: \(error.localizedDescription)"
        }
    }
}
```

## 调试技巧

### 性能监控
```swift
// 内存使用监控
let memoryFootprint = mach_task_basic_info()
print("Memory: \(memoryFootprint.resident_size / 1024 / 1024) MB")

// 图片缓存统计
print("Cache count: \(memoryCache.totalCostLimit)")
```

### 日志配置
```swift
// 开发环境详细日志，生产环境仅错误
#if DEBUG
let logLevel: OSLogType = .debug
#else
let logLevel: OSLogType = .error
#endif
```

## 开发规范参考

- 详见 `CODING_STANDARDS.md`
- 测试策略见 `TESTING_GUIDE.md`
- 开发进度见 `DEVELOPMENT_PROGRESS.md`
- 性能检查见 `PERFORMANCE_CHECKLIST.md`

## AI 开发提示

### 高效提示模板
```
请基于 MVVM-C 架构实现 [功能名称]：
1. 使用 Combine 响应式编程
2. 遵循项目代码规范
3. 包含错误处理和单元测试
4. 优化内存使用和 UI 响应

具体要求：[详细需求描述]
```

### 🔄 自动进度更新要求

**重要**: 每次完成开发任务后，AI 必须自动更新 `DEVELOPMENT_PROGRESS.md`：

1. **实时更新任务状态**:
   - 开始任务时：状态改为 `🔄 进行中`
   - 完成任务时：状态改为 `✅ 已完成`
   - 遇到阻塞时：状态改为 `❌ 已阻塞`

2. **更新进度统计**:
   - 自动计算各阶段完成百分比
   - 更新整体项目进度
   - 记录完成的具体内容

3. **记录开发日志**:
   - 在 `📝 开发记录` 部分添加当日完成的工作
   - 包含技术实现要点和遇到的问题

4. **里程碑检查**:
   - 自动检查是否达成里程碑
   - 更新里程碑完成状态

**这样确保项目进度始终保持最新，新的 AI 实例可以快速了解当前状态。**

### Token 优化策略
- 使用简化类名和变量名
- 避免重复代码注释
- 引用已有组件和模式
- 分步骤实现复杂功能