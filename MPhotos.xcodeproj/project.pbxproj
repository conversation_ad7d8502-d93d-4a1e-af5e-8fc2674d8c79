// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		B5FB0E892E3D4F74009CDE95 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B5FB0E672E3D4F73009CDE95 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B5FB0E6E2E3D4F73009CDE95;
			remoteInfo = MPhotos;
		};
		B5FB0E932E3D4F74009CDE95 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B5FB0E672E3D4F73009CDE95 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B5FB0E6E2E3D4F73009CDE95;
			remoteInfo = MPhotos;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B5FB0E6F2E3D4F73009CDE95 /* MPhotos.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MPhotos.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B5FB0E882E3D4F74009CDE95 /* MPhotosTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B5FB0E922E3D4F74009CDE95 /* MPhotosUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B5FB0E9A2E3D4F74009CDE95 /* Exceptions for "MPhotos" folder in "MPhotos" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B5FB0E6E2E3D4F73009CDE95 /* MPhotos */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B5FB0E712E3D4F73009CDE95 /* MPhotos */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B5FB0E9A2E3D4F74009CDE95 /* Exceptions for "MPhotos" folder in "MPhotos" target */,
			);
			path = MPhotos;
			sourceTree = "<group>";
		};
		B5FB0E8B2E3D4F74009CDE95 /* MPhotosTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosTests;
			sourceTree = "<group>";
		};
		B5FB0E952E3D4F74009CDE95 /* MPhotosUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B5FB0E6C2E3D4F73009CDE95 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E852E3D4F74009CDE95 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E8F2E3D4F74009CDE95 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B5FB0E662E3D4F73009CDE95 = {
			isa = PBXGroup;
			children = (
				B5FB0E712E3D4F73009CDE95 /* MPhotos */,
				B5FB0E8B2E3D4F74009CDE95 /* MPhotosTests */,
				B5FB0E952E3D4F74009CDE95 /* MPhotosUITests */,
				B5FB0E702E3D4F73009CDE95 /* Products */,
			);
			sourceTree = "<group>";
		};
		B5FB0E702E3D4F73009CDE95 /* Products */ = {
			isa = PBXGroup;
			children = (
				B5FB0E6F2E3D4F73009CDE95 /* MPhotos.app */,
				B5FB0E882E3D4F74009CDE95 /* MPhotosTests.xctest */,
				B5FB0E922E3D4F74009CDE95 /* MPhotosUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B5FB0E6E2E3D4F73009CDE95 /* MPhotos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FB0E9B2E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotos" */;
			buildPhases = (
				B5FB0E6B2E3D4F73009CDE95 /* Sources */,
				B5FB0E6C2E3D4F73009CDE95 /* Frameworks */,
				B5FB0E6D2E3D4F73009CDE95 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B5FB0E712E3D4F73009CDE95 /* MPhotos */,
			);
			name = MPhotos;
			packageProductDependencies = (
			);
			productName = MPhotos;
			productReference = B5FB0E6F2E3D4F73009CDE95 /* MPhotos.app */;
			productType = "com.apple.product-type.application";
		};
		B5FB0E872E3D4F74009CDE95 /* MPhotosTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FB0EA02E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotosTests" */;
			buildPhases = (
				B5FB0E842E3D4F74009CDE95 /* Sources */,
				B5FB0E852E3D4F74009CDE95 /* Frameworks */,
				B5FB0E862E3D4F74009CDE95 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B5FB0E8A2E3D4F74009CDE95 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B5FB0E8B2E3D4F74009CDE95 /* MPhotosTests */,
			);
			name = MPhotosTests;
			packageProductDependencies = (
			);
			productName = MPhotosTests;
			productReference = B5FB0E882E3D4F74009CDE95 /* MPhotosTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B5FB0E912E3D4F74009CDE95 /* MPhotosUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5FB0EA32E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotosUITests" */;
			buildPhases = (
				B5FB0E8E2E3D4F74009CDE95 /* Sources */,
				B5FB0E8F2E3D4F74009CDE95 /* Frameworks */,
				B5FB0E902E3D4F74009CDE95 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B5FB0E942E3D4F74009CDE95 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B5FB0E952E3D4F74009CDE95 /* MPhotosUITests */,
			);
			name = MPhotosUITests;
			packageProductDependencies = (
			);
			productName = MPhotosUITests;
			productReference = B5FB0E922E3D4F74009CDE95 /* MPhotosUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B5FB0E672E3D4F73009CDE95 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B5FB0E6E2E3D4F73009CDE95 = {
						CreatedOnToolsVersion = 16.4;
					};
					B5FB0E872E3D4F74009CDE95 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B5FB0E6E2E3D4F73009CDE95;
					};
					B5FB0E912E3D4F74009CDE95 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B5FB0E6E2E3D4F73009CDE95;
					};
				};
			};
			buildConfigurationList = B5FB0E6A2E3D4F73009CDE95 /* Build configuration list for PBXProject "MPhotos" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B5FB0E662E3D4F73009CDE95;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B5FB0E702E3D4F73009CDE95 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B5FB0E6E2E3D4F73009CDE95 /* MPhotos */,
				B5FB0E872E3D4F74009CDE95 /* MPhotosTests */,
				B5FB0E912E3D4F74009CDE95 /* MPhotosUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B5FB0E6D2E3D4F73009CDE95 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E862E3D4F74009CDE95 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E902E3D4F74009CDE95 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B5FB0E6B2E3D4F73009CDE95 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E842E3D4F74009CDE95 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5FB0E8E2E3D4F74009CDE95 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B5FB0E8A2E3D4F74009CDE95 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B5FB0E6E2E3D4F73009CDE95 /* MPhotos */;
			targetProxy = B5FB0E892E3D4F74009CDE95 /* PBXContainerItemProxy */;
		};
		B5FB0E942E3D4F74009CDE95 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B5FB0E6E2E3D4F73009CDE95 /* MPhotos */;
			targetProxy = B5FB0E932E3D4F74009CDE95 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B5FB0E9C2E3D4F74009CDE95 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B5FB0E9D2E3D4F74009CDE95 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B5FB0E9E2E3D4F74009CDE95 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B5FB0E9F2E3D4F74009CDE95 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B5FB0EA12E3D4F74009CDE95 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Debug;
		};
		B5FB0EA22E3D4F74009CDE95 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Release;
		};
		B5FB0EA42E3D4F74009CDE95 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Debug;
		};
		B5FB0EA52E3D4F74009CDE95 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B5FB0E6A2E3D4F73009CDE95 /* Build configuration list for PBXProject "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FB0E9E2E3D4F74009CDE95 /* Debug */,
				B5FB0E9F2E3D4F74009CDE95 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FB0E9B2E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FB0E9C2E3D4F74009CDE95 /* Debug */,
				B5FB0E9D2E3D4F74009CDE95 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FB0EA02E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotosTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FB0EA12E3D4F74009CDE95 /* Debug */,
				B5FB0EA22E3D4F74009CDE95 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5FB0EA32E3D4F74009CDE95 /* Build configuration list for PBXNativeTarget "MPhotosUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5FB0EA42E3D4F74009CDE95 /* Debug */,
				B5FB0EA52E3D4F74009CDE95 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B5FB0E672E3D4F73009CDE95 /* Project object */;
}
