# MPhotos 架构速查表

## 🏗️ 分层架构概览

```
┌─────────────────────┐
│   Presentation      │ ← ViewControllers, ViewModels, Coordinators
├─────────────────────┤
│   Domain           │ ← UseCases, Services, Managers
├─────────────────────┤
│   Data             │ ← Repositories, Cache, Models
├─────────────────────┤
│   Core             │ ← Extensions, Utilities, DI
└─────────────────────┘
```

## 📋 核心组件速查

### MediaManager
**位置**: `Domain/Managers/MediaManager.swift`
**职责**: PhotoKit 封装、权限管理、资源 CRUD
```swift
protocol MediaManaging {
    func requestAuthorization() async throws -> PHAuthorizationStatus
    func fetchAlbums(ofType: AlbumType) -> [Album]
    func fetchAssets(in album: Album?) -> PHFetchResult<PHAsset>
    func createAlbum(title: String) async throws -> Album
    func deleteAssets(_ assets: [PHAsset]) async throws
}
```

### CacheManager  
**位置**: `Data/Cache/CacheManager.swift`
**职责**: 二级缓存（NSCache + PHCachingImageManager）
```swift
protocol CacheManaging {
    func requestImage(for asset: PHAsset, targetSize: CGSize) async -> UIImage?
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize)
    func clearCache()
}
```

### BaseViewModel
**位置**: `Presentation/Base/BaseViewModel.swift`
**职责**: MVVM 通用状态管理
```swift
class BaseViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var error: Error?
    
    func handleError(_ error: Error) { }
    func startLoading() { }
    func stopLoading() { }
}
```

### Coordinator
**位置**: `Presentation/Coordinators/`
**职责**: 导航流程控制
```swift
protocol Coordinating: AnyObject {
    var navigationController: UINavigationController { get }
    func start()
    func coordinate(to coordinator: Coordinating)
}
```

## 🔄 数据流向图

```mermaid
graph TD
    A[User Action] --> B[ViewController]
    B --> C[ViewModel]
    C --> D[UseCase]
    D --> E[Service/Manager]
    E --> F[Repository]
    F --> G[Cache/PhotoKit/CoreData]
    
    G --> F
    F --> E
    E --> D
    D --> C
    C --> B
    B --> H[UI Update]
```

## 🎯 关键接口定义

### Album 数据模型
```swift
struct Album {
    let identifier: String
    let title: String
    let type: AlbumType
    let assetCount: Int
    let collection: PHAssetCollection?
    
    func fetchAssets() -> PHFetchResult<PHAsset>
    func getCoverImage(size: CGSize) async -> UIImage?
}

enum AlbumType {
    case smartAlbum, userAlbum, sharedAlbum, moment
}
```

### Asset 扩展接口
```swift
extension PHAsset {
    var isInCloud: Bool { }
    var fileSize: Int64 { }
    
    func thumbnail(size: CGSize) async -> UIImage?
    func fullResolutionImage() async -> UIImage?
    func exportVideo() async -> URL?
}
```

### Error 处理
```swift
enum MPhotosError: LocalizedError {
    case unauthorized
    case assetNotFound
    case networkError(Error)
    case cacheError(String)
    
    var errorDescription: String? { }
}
```

## 🎨 UI 组件层次

### 主界面结构
```
TabBarController
├── AlbumsNavigationController
│   ├── AlbumsViewController
│   └── MediaGridViewController
├── PhotosNavigationController
│   ├── MediaGridViewController
│   └── MediaViewerViewController
└── SettingsNavigationController
    └── SettingsViewController
```

### CollectionView 架构
```swift
// Cell 标准结构
class MediaCell: UICollectionViewCell {
    static let identifier = "MediaCell"
    
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var durationLabel: UILabel!
    @IBOutlet weak var cloudIcon: UIImageView!
    
    private var requestID: PHImageRequestID?
    
    func configure(with asset: PHAsset) { }
    override func prepareForReuse() { }
}
```

## ⚡ 性能关键点

### 缓存策略
```swift
// L1: NSCache (内存) - 50MB, 200张
// L2: PHCachingImageManager (系统缓存)

let cacheKey = "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))"
```

### 预加载逻辑
```swift
// 可见区域 ± 20 个项目预加载
let visibleRange = collectionView.indexPathsForVisibleItems
let preloadRange = expandRange(visibleRange, by: 20)
cacheManager.preloadAssets(assetsInRange(preloadRange), targetSize: thumbnailSize)
```

### 内存管理
```swift
// Cell 中必须实现
override func prepareForReuse() {
    super.prepareForReuse()
    
    if let requestID = currentRequestID {
        PHImageManager.default().cancelImageRequest(requestID)
    }
    imageView.image = nil
}
```

## 🧪 测试架构

### Mock 对象层次
```
MockMediaManager: MediaManaging
├── albumsToReturn: [Album]
├── assetsToReturn: [PHAsset] 
├── shouldFail: Bool
└── errorToThrow: Error

MockCacheManager: CacheManaging
├── storage: [String: UIImage]
├── storeImageCalled: Bool
└── loadImageCalled: Bool
```

### 测试分类
- **Unit Tests**: ViewModels, Services, Utils
- **Integration Tests**: Repository + Manager 组合
- **UI Tests**: 关键用户流程
- **Performance Tests**: 内存、响应时间、启动速度

## 🛠️ 开发工具链

### 项目配置
```swift
// Info.plist 关键配置
NSPhotoLibraryUsageDescription: "需要访问相册来浏览和管理照片"
NSPhotoLibraryAddUsageDescription: "需要保存编辑后的照片到相册"
```

### Build Settings
- **iOS Deployment Target**: 16.4
- **Swift Language Version**: 5.9
- **Code Coverage**: Enabled for Debug

### 依赖管理
```swift
// 推荐最小依赖
- Combine (系统框架)
- PhotoKit (系统框架)  
- AVFoundation (视频处理)
- CoreImage (图像处理)
```

## 🔍 调试工具

### 性能监控
```swift
// 内存使用
let memoryUsage = PerformanceMonitor.memoryUsage()
print("Memory: \(memoryUsage / 1024 / 1024) MB")

// 图片缓存统计
print("Cache count: \(CacheManager.shared.cacheCount)")
print("Cache size: \(CacheManager.shared.cacheSize) MB")
```

### 日志系统
```swift
enum LogLevel {
    case debug, info, warning, error
}

struct Logger {
    static func log(_ message: String, level: LogLevel = .info) {
        #if DEBUG
        print("[\(level)] \(message)")
        #endif
    }
}
```

## 📱 平台适配

### 权限处理
```swift
// 权限状态处理
switch PHPhotoLibrary.authorizationStatus(for: .readWrite) {
case .notDetermined:
    // 首次请求权限
case .denied, .restricted:
    // 显示设置引导
case .limited:
    // 部分权限，显示管理界面
case .authorized:
    // 完整权限，正常使用
}
```

### 设备适配
```swift
// 根据设备性能调整
let devicePerformance = UIDevice.current.devicePerformanceLevel
let thumbnailSize = devicePerformance.thumbnailSize
let cacheLimit = devicePerformance.cacheLimit
```

## 🚀 发布检查

### 性能指标
- 启动时间: <2s (冷启动)
- 内存使用: <150MB (正常)
- 滚动帧率: 60fps
- 电池续航: <120% 相比系统相册

### 功能完整性
- [ ] 相册浏览 ✓
- [ ] 照片查看 ✓  
- [ ] 基础编辑 ✓
- [ ] 相册管理 ✓
- [ ] 权限处理 ✓
- [ ] 错误处理 ✓

---

**快速导航**: `cmd+f` 搜索组件名或接口名快速定位