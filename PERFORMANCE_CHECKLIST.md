# MPhotos 性能检查清单

## 🎯 性能目标
- **启动时间**: < 2s (冷启动), < 0.5s (热启动)
- **内存使用**: < 150MB (正常浏览), < 300MB (峰值)
- **UI 响应**: 60fps 滚动, < 100ms 触摸响应
- **电池续航**: 相比系统 Photos 应用 < 120%

## 📱 内存管理检查

### ✅ 内存使用监控
```swift
// 监控内存使用的工具代码
extension PerformanceMonitor {
    static func checkMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}
```

### 检查项目
- [ ] **缓存大小合理**: NSCache 限制 50MB, 200张图片
- [ ] **图片请求及时取消**: Cell 复用时取消 PHImageRequest
- [ ] **大图片按需加载**: 仅在需要时加载全尺寸图片
- [ ] **内存警告处理**: 收到警告时清理非关键缓存
- [ ] **循环引用检测**: 使用 `weak self` 避免闭包循环引用
- [ ] **观察者及时移除**: deinit 中移除 NotificationCenter 观察者

### 内存泄漏检测
```swift
// Xcode Instruments: Product → Profile → Leaks
// 单元测试中检测
func testViewModelMemoryLeak() {
    weak var weakViewModel: MediaGridViewModel?
    
    autoreleasepool {
        let viewModel = MediaGridViewModel(service: MockService())
        weakViewModel = viewModel
        viewModel.loadAssets()  // 执行业务逻辑
    }
    
    XCTAssertNil(weakViewModel, "ViewModel 应该被释放")
}
```

## 🖼️ 图片加载性能

### ✅ 缓存策略检查
- [ ] **L1缓存 (NSCache)**: 当前会话快速访问
- [ ] **L2缓存 (PHCaching)**: 系统优化的预缓存
- [ ] **缓存键优化**: `assetID_size_quality` 格式
- [ ] **缓存失效策略**: LRU + 内存压力感知
- [ ] **预加载范围**: 可见区域 ±20 个项目

### 图片加载优化
```swift
// ✅ 推荐的图片请求配置
let options = PHImageRequestOptions()
options.deliveryMode = .opportunistic  // 先低质量后高质量
options.isNetworkAccessAllowed = false // 首次不下载 iCloud
options.resizeMode = .fast             // 快速缩放
options.isSynchronous = false          // 异步加载

// ✅ GPU 友好的尺寸对齐
func alignSizeForGPU(_ size: CGSize) -> CGSize {
    return CGSize(
        width: ceil(size.width / 16) * 16,
        height: ceil(size.height / 16) * 16
    )
}
```

### 检查项目
- [ ] **缩略图尺寸优化**: 根据 Cell 大小和屏幕密度计算
- [ ] **批量请求管理**: 避免同时发起过多请求
- [ ] **请求优先级**: 可见项目优先于预加载项目
- [ ] **取消机制**: Cell 离开屏幕时取消请求
- [ ] **降级策略**: 网络不可用时使用本地缓存

## 📱 UI 响应性检查

### ✅ 主线程性能
- [ ] **UI 更新**: 所有 UI 更新在主线程执行
- [ ] **后台处理**: 图片解码、数据处理在后台队列
- [ ] **批量更新**: 使用 `performBatchUpdates` 优化 CollectionView
- [ ] **差分更新**: 仅更新变化的项目，避免全量刷新
- [ ] **延迟加载**: 非关键 UI 元素延迟初始化

### 滚动性能优化
```swift
// ✅ 滚动时降低质量提升响应
extension UIScrollViewDelegate {
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // 滚动开始，使用快速模式
        ImageLoadingOptions.shared.deliveryMode = .fastFormat
        pauseNonCriticalTasks()
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        // 滚动结束，恢复高质量
        ImageLoadingOptions.shared.deliveryMode = .highQualityFormat
        resumeNonCriticalTasks()
    }
}
```

### 检查项目
- [ ] **CollectionView 配置**: `isPrefetchingEnabled = true`
- [ ] **Cell 复用优化**: `prepareForReuse` 中清理状态
- [ ] **布局缓存**: 避免重复计算 Cell 尺寸
- [ ] **动画性能**: 使用硬件加速的动画属性
- [ ] **触摸响应**: 关键按钮响应时间 < 100ms

## ⚡ 启动性能检查

### ✅ 冷启动优化
- [ ] **延迟初始化**: 非关键服务延迟到首次使用
- [ ] **权限请求**: 在用户需要时才请求相册权限
- [ ] **数据预加载**: 仅加载首屏需要的数据
- [ ] **动态库延迟加载**: 大型依赖库按需加载
- [ ] **启动时间监控**: 使用 MetricKit 监控启动性能

### 启动性能监控
```swift
// App 启动时间监控
class AppStartupMonitor {
    static let shared = AppStartupMonitor()
    private let startTime = CFAbsoluteTimeGetCurrent()
    
    func recordLaunchCompletion() {
        let launchTime = CFAbsoluteTimeGetCurrent() - startTime
        print("App launch time: \(launchTime)s")
        
        // 上报性能数据
        Analytics.record(event: "app_launch_time", value: launchTime)
    }
}
```

### 检查项目
- [ ] **主线程阻塞**: 启动时避免长时间主线程任务
- [ ] **网络请求**: 启动时避免同步网络请求
- [ ] **文件I/O**: 最小化启动时的磁盘读写
- [ ] **复杂计算**: 将复杂初始化移到后台队列

## 🔋 电池性能检查

### ✅ 电池使用优化
- [ ] **CPU 使用**: 避免不必要的后台计算
- [ ] **网络请求**: 批量处理，减少频繁连接
- [ ] **定位服务**: 仅在需要时使用，及时停止
- [ ] **后台处理**: 合理使用后台任务，及时完成
- [ ] **屏幕亮度**: 避免强制提高屏幕亮度

### 电池监控
```swift
// 电池状态监控
extension PerformanceMonitor {
    func monitorBatteryUsage() {
        UIDevice.current.isBatteryMonitoringEnabled = true
        
        NotificationCenter.default.addObserver(
            forName: UIDevice.batteryStateDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            let batteryLevel = UIDevice.current.batteryLevel
            let batteryState = UIDevice.current.batteryState
            
            // 低电量时优化性能
            if batteryLevel < 0.2 {
                self.enableLowPowerMode()
            }
        }
    }
}
```

## 📊 性能测试与监控

### ✅ 自动化性能测试
```swift
class PerformanceTests: XCTestCase {
    func testScrollingPerformance() {
        measure(metrics: [XCTMemoryMetric(), XCTCPUMetric()]) {
            // 模拟滚动 1000 个项目
            let collectionView = createTestCollectionView()
            for i in 0..<1000 {
                let indexPath = IndexPath(item: i, section: 0)
                _ = collectionView.dataSource?.collectionView(
                    collectionView, cellForItemAt: indexPath
                )
            }
        }
    }
    
    func testImageCachingPerformance() {
        measure(metrics: [XCTMemoryMetric()]) {
            let cacheManager = CacheManager()
            let images = createTestImages(count: 100)
            
            Task {
                for (index, image) in images.enumerated() {
                    await cacheManager.store(image, for: "key_\(index)")
                }
            }
        }
    }
}
```

### 生产监控
```swift
// 使用 MetricKit 监控生产性能
import MetricKit

class ProductionMetricsSubscriber: NSObject, MXMetricManagerSubscriber {
    override init() {
        super.init()
        MXMetricManager.shared.add(self)
    }
    
    func didReceive(_ payloads: [MXMetricPayload]) {
        for payload in payloads {
            // 启动时间
            if let launchMetrics = payload.applicationLaunchMetrics {
                print("Launch time: \(launchMetrics.histogrammedTimeToFirstDraw)")
            }
            
            // 内存使用
            if let memoryMetrics = payload.memoryMetrics {
                print("Peak memory: \(memoryMetrics.peakMemoryUsage)")
            }
            
            // 电池使用
            if let powerMetrics = payload.cellularConditionMetrics {
                print("Battery usage: \(powerMetrics)")
            }
        }
    }
}
```

## ⚡ 性能优化最佳实践

### 代码级优化
```swift
// ✅ 推荐：使用 lazy 延迟初始化
private lazy var expensiveCalculation: ExpensiveType = {
    return ExpensiveType.performComplexCalculation()
}()

// ✅ 推荐：合并多个属性更新
func updateUI(with data: DataType) {
    UIView.performWithoutAnimation {
        titleLabel.text = data.title
        subtitleLabel.text = data.subtitle
        imageView.image = data.image
    }
}

// ✅ 推荐：使用 DispatchQueue.concurrentPerform 并行处理
DispatchQueue.concurrentPerform(iterations: items.count) { index in
    processItem(items[index])
}
```

### 架构级优化
- [ ] **职责分离**: 避免 Massive View Controller
- [ ] **数据流优化**: 减少不必要的数据传递
- [ ] **依赖注入**: 避免单例滥用
- [ ] **协议导向**: 使用协议提高代码灵活性

## 📋 发布前性能清单

### 关键指标验证
- [ ] **启动时间**: 冷启动 < 2s, 热启动 < 0.5s
- [ ] **内存峰值**: 正常使用 < 150MB
- [ ] **滚动帧率**: 60fps 无丢帧
- [ ] **电池续航**: 1小时使用 < 10% 电量
- [ ] **崩溃率**: < 0.1% 崩溃率

### 设备兼容性测试
- [ ] **iPhone SE (最低配置)**: 性能可接受
- [ ] **iPhone 15 Pro Max**: 充分利用硬件性能
- [ ] **低内存设备**: 2GB RAM 设备正常运行
- [ ] **低电量模式**: 功能降级但可用

### 工具验证
- [ ] **Instruments Time Profiler**: 无明显性能热点
- [ ] **Instruments Allocations**: 内存使用合理
- [ ] **Instruments Leaks**: 无内存泄漏
- [ ] **Instruments Energy Log**: 电池使用优化

---

**性能原则**: 测量优于猜测，用户体验优于技术指标