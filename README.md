# MPhotos - 高性能 iOS Photos 克隆应用

## 🎯 项目状态
- **当前阶段**: 📋 架构设计完成，准备开始 Phase 1 开发
- **预计工期**: 10-14 周
- **技术栈**: Swift 5.9 + UIKit/SwiftUI + PhotoKit + Core Data

## 🧭 快速导航：我应该看哪个文档？

### 🔰 刚接手项目？先看这里
1. **README.md** (本文档) - 项目概览和导航
2. **MPhotos总文档.md** - 完整的技术架构设计
3. **CLAUDE.md** - Claude Code 开发入门

### 📅 根据开发阶段选择文档

#### Phase 1: 基础架构搭建 (2-3周)
**主要任务**: DI容器、PhotoKit封装、Core Data、缓存系统

🔍 **需要的文档**:
- 📋 **DEVELOPMENT_PROGRESS.md** - 查看 Phase 1 任务清单
- 🏗️ **ARCHITECTURE_QUICK_REF.md** - 核心组件接口定义
- 📝 **CODING_STANDARDS.md** - 代码规范和架构模式
- 🧪 **TESTING_GUIDE.md** - 单元测试模板

```bash
# Phase 1 关键检查点
- [ ] MediaManager 基础功能可用
- [ ] 二级缓存系统运行
- [ ] 权限管理流程完整
- [ ] Core Data 数据模型创建
```

#### Phase 2: 基础UI开发 (3-4周)
**主要任务**: TabBar框架、相册列表、照片网格、图片浏览器

🔍 **需要的文档**:
- 📋 **DEVELOPMENT_PROGRESS.md** - Phase 2 任务进度
- 🚀 **PERFORMANCE_CHECKLIST.md** - UI响应性检查
- 🏗️ **ARCHITECTURE_QUICK_REF.md** - UI组件层次结构
- 📝 **CODING_STANDARDS.md** - UIKit开发规范

```bash
# Phase 2 关键检查点  
- [ ] 流畅的60fps滚动
- [ ] 完整的照片浏览体验
- [ ] 转场动画和手势交互
- [ ] 缩略图系统优化
```

#### Phase 3: 编辑功能开发 (3-4周)
**主要任务**: 多选模式、图片编辑器、滤镜、相册管理

🔍 **需要的文档**:
- 📋 **DEVELOPMENT_PROGRESS.md** - Phase 3 功能清单
- 🧪 **TESTING_GUIDE.md** - 编辑功能测试用例
- 🚀 **PERFORMANCE_CHECKLIST.md** - 内存管理检查
- 🏗️ **ARCHITECTURE_QUICK_REF.md** - 数据流向图

```bash
# Phase 3 关键检查点
- [ ] 编辑操作无内存泄漏
- [ ] 撤销/重做功能完整
- [ ] 批量操作性能良好
- [ ] 编辑历史数据持久化
```

#### Phase 4: 优化和发布 (2-3周)
**主要任务**: 性能优化、测试完善、无障碍支持

🔍 **需要的文档**:
- 🚀 **PERFORMANCE_CHECKLIST.md** - 发布前性能验证
- 🧪 **TESTING_GUIDE.md** - 集成测试和UI测试
- 📋 **DEVELOPMENT_PROGRESS.md** - 最终验收标准

```bash
# Phase 4 发布检查
- [ ] 启动时间 < 2s
- [ ] 内存使用 < 150MB
- [ ] 测试覆盖率 > 70%
- [ ] 无障碍功能完整
```

## 🚨 遇到问题时的文档查找

### 性能问题
1. **🚀 PERFORMANCE_CHECKLIST.md** - 系统性性能检查
2. **🏗️ ARCHITECTURE_QUICK_REF.md** - 缓存策略和优化点
3. **📝 CODING_STANDARDS.md** - 性能编码规范

### 测试问题
1. **🧪 TESTING_GUIDE.md** - 测试模板和Mock策略
2. **📝 CODING_STANDARDS.md** - 测试代码规范
3. **🏗️ ARCHITECTURE_QUICK_REF.md** - 测试架构参考

### 架构设计问题
1. **MPhotos总文档.md** - 完整技术设计
2. **🏗️ ARCHITECTURE_QUICK_REF.md** - 核心组件速查
3. **📝 CODING_STANDARDS.md** - MVVM-C实现标准

### AI开发问题  
1. **CLAUDE.md** - Claude Code使用指南
2. **📝 CODING_STANDARDS.md** - AI友好代码规范
3. **🧪 TESTING_GUIDE.md** - 自动化测试模板

## 📂 文档目录结构

```
MPhotos/
├── README.md                    # 📖 本文档 - 项目导航
├── MPhotos总文档.md              # 📚 完整技术架构设计  
├── CLAUDE.md                    # 🤖 Claude Code开发指引
├── DEVELOPMENT_PROGRESS.md      # 📋 开发进度管理
├── CODING_STANDARDS.md          # 📝 代码规范和最佳实践
├── TESTING_GUIDE.md             # 🧪 测试策略和模板
├── PERFORMANCE_CHECKLIST.md     # 🚀 性能监控和优化
└── ARCHITECTURE_QUICK_REF.md    # 🏗️ 架构组件速查表
```

## 🎯 不同角色的使用建议

### 👨‍💻 开发者
**首次接手**: README → CLAUDE.md → DEVELOPMENT_PROGRESS.md  
**编码阶段**: CODING_STANDARDS + ARCHITECTURE_QUICK_REF  
**测试阶段**: TESTING_GUIDE + PERFORMANCE_CHECKLIST

### 🤖 AI助手 (Claude Code)
**任务理解**: README → 对应Phase文档  
**代码生成**: CLAUDE.md + CODING_STANDARDS  
**架构查询**: ARCHITECTURE_QUICK_REF  
**测试编写**: TESTING_GUIDE

### 👔 项目管理
**进度跟踪**: DEVELOPMENT_PROGRESS.md  
**质量把控**: PERFORMANCE_CHECKLIST + TESTING_GUIDE  
**里程碑验收**: 各Phase检查点

## ⚡ 快速命令参考

```bash
# 快速查看当前阶段任务
grep -A 10 "Phase [0-9].*进行中" DEVELOPMENT_PROGRESS.md

# 检查代码规范
grep -n "✅\|❌" CODING_STANDARDS.md

# 查看性能检查清单
grep -A 5 "\[ \]" PERFORMANCE_CHECKLIST.md

# 查找特定组件架构
grep -A 10 "组件名" ARCHITECTURE_QUICK_REF.md
```

## 🔄 文档更新策略

### 开发过程中需要更新的文档
- **DEVELOPMENT_PROGRESS.md**: 实时更新任务状态和进度
- **CLAUDE.md**: 添加新的代码模板和经验总结
- **README.md**: 更新项目状态和阶段信息

### 相对稳定的文档
- **CODING_STANDARDS.md**: 除非架构调整，否则保持稳定
- **ARCHITECTURE_QUICK_REF.md**: 核心架构确定后较少变动
- **TESTING_GUIDE.md**: 测试策略相对固定

---

## 🎉 开始开发

### 现在应该做什么？
1. ✅ 已完成架构设计和文档准备
2. 🔲 查看 **DEVELOPMENT_PROGRESS.md** Phase 1 任务
3. 🔲 按照 **CLAUDE.md** 指引开始编码
4. 🔲 遵循 **CODING_STANDARDS.md** 编写代码

**下一步行动**: 开始 Phase 1 第一个任务 - 项目初始化和配置

---

**💡 提示**: 在每个开发阶段开始时，重新阅读本README对应的阶段指导，确保使用正确的文档组合。